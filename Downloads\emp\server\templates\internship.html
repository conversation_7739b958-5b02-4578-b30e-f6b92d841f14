{% extends "header.html" %}
{% load static %}
{% block content %}


  <section class="internship-page">

    <!-- Header -->
    <div class="internship-page-header">
  <h2 class="internship-title">Internship Opportunities</h2>
  <p class="internship-subtitle">
    Gain hands-on experience and learn from industry experts. Explore our current internship roles.
  </p>
</div>

<!-- New animated sections -->
<div class="internship-benefits">
  <div class="benefits-container">
    <h3 class="section-title">Benefits of Interning at Click Digitals</h3>
    <div class="benefits-grid">
      <div class="benefit-card">
        <div class="benefit-icon">🚀</div>
        <h4>Real-World Projects</h4>
        <p>Work on actual client projects and see your contributions make a real impact from day one.</p>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">👥</div>
        <h4>Mentorship Program</h4>
        <p>Get paired with experienced professionals who will guide your learning journey and career growth.</p>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">📚</div>
        <h4>Skill Development</h4>
        <p>Access to workshops, training sessions, and cutting-edge tools to enhance your technical abilities.</p>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">🏆</div>
        <h4>Career Pathway</h4>
        <p>Top-performing interns often receive full-time offers to continue their journey with us.</p>
      </div>
    </div>
  </div>
</div>

<div class="internship-requirements">
  <div class="requirements-container">
    <h3 class="section-title">What We're Looking For</h3>
    <div class="requirements-content">
      <div class="requirements-text">
        <p class="highlight-text">We welcome passionate individuals who are eager to learn and grow in a dynamic environment.</p>
        <ul class="requirements-list">
          <li>Currently pursuing a degree in relevant field</li>
          <li>Strong problem-solving and analytical skills</li>
          <li>Excellent communication and teamwork abilities</li>
          <li>Willingness to learn new technologies and methodologies</li>
          <li>Previous projects or portfolio (preferred but not required)</li>
        </ul>
      </div>
      <div class="requirements-visual">
        <div class="floating-elements">
          <div class="floating-circle"></div>
          <div class="floating-triangle"></div>
          <div class="floating-square"></div>
        </div>
      </div>
    </div>
  </div>
</div>

    <!-- Internship List -->
     <div class="container">
      <h2 class="section-title" style="color: rgb(13, 162, 243);">Internship Opportunities</h2>
      <p class="section-subtitle">
          Gain hands-on experience and learn from industry experts. Explore our current internship roles.
      </p>
    <div class="internship-list-wrapper">
      {% for i in intern %}
      <div class="internship-item">
        <img src="/media/{{i.background_image}}" alt="Internship Image" loading="lazy" class="internship-image">
        <h3 class="internship-name">{{i.position}}</h3>
        <p class="internship-description">{{i.description | truncatechars:100}}</p>
        <a href="{% url 'applicant' i.id %}" class="btn btn-primary" >Apply Now</a>
      </div>
      {% endfor %}
    </div>
    </div>

<div class="internship-process">
  <div class="process-container">
    <h3 class="section-title">Application Process</h3>
    <div class="process-timeline">
      <div class="timeline-item">
        <div class="timeline-dot">1</div>
        <div class="timeline-content">
          <h4>Submit Application</h4>
          <p>Fill out our online application form with your resume and photo</p>
        </div>
      </div>
      <div class="timeline-item">
        <div class="timeline-dot">2</div>
        <div class="timeline-content">
          <h4>Initial Review</h4>
          <p>Our team reviews your application ,shortlists candidates and shedules  the interview with a task.</p>
        </div>
      </div>
      <div class="timeline-item">
        <div class="timeline-dot">3</div>
        <div class="timeline-content">
          <h4>Interview Round</h4>
          <p>Technical and behavioral interview with our team leads</p>
        </div>
      </div>
      <div class="timeline-item">
        <div class="timeline-dot">4</div>
        <div class="timeline-content">
          <h4>Welcome Aboard</h4>
          <p>Onboarding and orientation to kick-start your internship journey</p>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="internship-testimonials">
  <div class="testimonials-container">
    <h3 class="section-title">What Our Interns Say</h3>
    <div class="testimonials-slider">
      <div class="testimonial-card active">
        <div class="testimonial-content">
          <p>"This internship transformed my understanding of real-world development. The mentorship was exceptional!"</p>
          <div class="testimonial-author">
            <div class="author-avatar"></div>
            <div class="author-info">
              <h5>Sabinam Mahato</h5>
              <span>React and Django Intern</span>
            </div>
          </div>
        </div>
      </div>
      <div class="testimonial-card">
        <div class="testimonial-content">
          <p>"I learned more in 3 months here than I did in my entire final year. Amazing team and culture!"</p>
          <div class="testimonial-author">
            <div class="author-avatar"></div>
            <div class="author-info">
              <h5>Michael Chen</h5>
              <span>Data Science Intern</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<style>

/* Requirements Section */
.internship-requirements {
  margin: 60px 0;
  padding: 40px 20px;
  background: rgba(5, 5, 47, 0.3);
  border-radius: 20px;
}

.requirements-container {
  max-width: 1200px;
  margin: 0 auto;
}

.requirements-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  margin-top: 30px;
}

.highlight-text {
  font-size: 20px;
  color: #87ceeb;
  font-weight: 600;
  margin-bottom: 25px;
  text-align: center;
}

.requirements-list {
  list-style: none;
  padding: 0;
  max-width: 800px;
  margin: 0 auto;
}

.requirements-list li {
  color: #fdfafa;
  margin-bottom: 15px;
  padding-left: 30px;
  position: relative;
  font-size: 16px;
  line-height: 1.5;
}

.requirements-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #20b2aa;
  font-weight: bold;
}

/* Responsive styles */
@media (max-width: 768px) {
  .internship-requirements {
    padding: 30px 15px;
  }

  .highlight-text {
    font-size: 18px;
  }

  .requirements-list li {
    font-size: 15px;
  }
}

* {
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f9f9f9;
  color: #cdbebe;
  overflow-x: hidden;
}

/* Main container */
.internship-page {
  margin-top: 0px;
  padding: 50px 20px;
  background: linear-gradient(90deg, #080f1c, #101d36);
  min-height: 100vh;
  animation: fadeInBackground 1s ease-out;
}

@keyframes fadeInBackground {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Header section styling */
.internship-page-header {
  padding-top: 20px;
  max-width: 1200px;
  margin: 0 auto  auto;
  text-align: center;
  color: aliceblue;
  padding: 40px 20px;
  position: relative;
  overflow: hidden;
  animation: fadeInDown 1s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animated background for header */
.internship-page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(135, 206, 235, 0.1) 0%, transparent 70%);
  animation: backgroundPulse 4s ease-in-out infinite;
  z-index: -1;
}

@keyframes backgroundPulse {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.1; }
  50% { transform: scale(1.1) rotate(180deg); opacity: 0.2; }
}

/* Main title styling */
.internship-title {
  font-size: clamp(28px, 5vw, 48px);
  margin-bottom: 50px;
  font-weight: 700;
  background: linear-gradient(45deg, #37aad7, #1cd3ca, #209ecf);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 30px rgba(20, 179, 242, 0.3);
  position: relative;
  animation: slideInFromTop 1s ease-out, gradientShift 3s ease-in-out infinite;
}

/* Subtitle styling */
.internship-subtitle {
  font-size: clamp(16px, 3vw, 22px);
  color: #b1b0b0;
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  position: relative;
  animation: slideInFromBottom 1s ease-out 0.3s both;
  opacity: 0;
}

/* Slide in from top animation */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from bottom animation */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Gradient shift animation for title */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Decorative elements */
.internship-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, #87ceeb, #20b2aa, #87ceeb, transparent);
  border-radius: 2px;
  animation: expandLine 1.5s ease-out 0.5s both;
}

@keyframes expandLine {
  from { width: 0; }
  to { width: 80%; }
}

/* Floating particles effect */
.internship-page-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(135, 206, 235, 0.3) 1px, transparent 1px),
    radial-gradient(circle at 80% 40%, rgba(32, 178, 170, 0.3) 1px, transparent 1px),
    radial-gradient(circle at 40% 80%, rgba(135, 206, 235, 0.3) 1px, transparent 1px);
  animation: floatingParticles 8s linear infinite;
  z-index: -1;
}

@keyframes floatingParticles {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

/* Hover effects */
.internship-page-header:hover .internship-title {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.internship-page-header:hover .internship-subtitle {
  color: #d1d1d1;
  transition: color 0.3s ease;
}

/* Responsive design */
@media (max-width: 768px) {
  .internship-page {
    padding: 20px 10px;
    margin-top: 0px;
  }

  .internship-page-header {
    padding: 20px 15px;
    margin-bottom: 20px;
  }

  .internship-title {
    font-size: 28px;
    margin-bottom: 10px;
  }

  .internship-subtitle {
    font-size: 16px;
    padding: 0 10px;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 10px;
  }

  .benefit-card {
    padding: 20px;
  }

  .benefit-icon {
    font-size: 36px;
    margin-bottom: 15px;
  }

  .benefit-card h4 {
    font-size: 20px;
  }

  .benefit-card p {
    font-size: 14px;
  }

  .requirements-content {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 15px;
  }

  .highlight-text {
    font-size: 18px;
    margin-bottom: 20px;
  }

  .requirements-list li {
    font-size: 14px;
    margin-bottom: 12px;
    padding-left: 25px;
  }

  .process-timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
    padding-left: 70px;
    margin-bottom: 40px;
  }

  .timeline-dot {
    left: 30px;
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .timeline-content {
    width: calc(100% - 70px);
    padding: 20px;
  }

  .timeline-content h4 {
    font-size: 18px;
  }

  .timeline-content p {
    font-size: 14px;
  }

  .testimonials-container {
    padding: 0 15px;
  }

  .testimonial-card {
    padding: 25px;
  }

  .testimonial-content p {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .testimonial-author {
    flex-direction: column;
    gap: 15px;
  }

  .author-avatar {
    width: 50px;
    height: 50px;
  }

  .author-info h5 {
    font-size: 16px;
  }

  .author-info span {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .internship-page {
    padding: 40px 5px;
    margin-top: 0px;
  }

  .internship-page-header {
    padding: 40px 10px;
  }

  .internship-title {
    font-size: 24px;
  }

  .internship-subtitle {
    font-size: 14px;
  }

  .section-title {
    font-size: 20px;
    margin-bottom: 30px;
  }

  .benefit-card {
    padding: 15px;
  }

  .benefit-icon {
    font-size: 32px;
  }

  .benefit-card h4 {
    font-size: 18px;
  }

  .benefit-card p {
    font-size: 13px;
  }

  .requirements-content {
    padding: 0 10px;
  }

  .highlight-text {
    font-size: 16px;
  }

  .requirements-list li {
    font-size: 13px;
    padding-left: 22px;
  }

  .timeline-item {
    padding-left: 60px;
    margin-bottom: 30px;
  }

  .timeline-dot {
    width: 40px;
    height: 40px;
    font-size: 18px;
    left: 25px;
  }

  .timeline-content {
    width: calc(100% - 60px);
    padding: 15px;
  }

  .timeline-content h4 {
    font-size: 16px;
  }

  .timeline-content p {
    font-size: 13px;
  }

  .testimonial-card {
    padding: 20px;
  }

  .testimonial-content p {
    font-size: 14px;
  }

  .author-avatar {
    width: 40px;
    height: 40px;
  }

  .author-info h5 {
    font-size: 14px;
  }

  .author-info span {
    font-size: 11px;
  }

  .internship-item {
    padding: 15px;
  }

  .internship-image {
    height: 140px;
  }

  .internship-name {
    font-size: 18px;
  }

  .internship-description {
    font-size: 13px;
  }

  .btn-apply {
    padding: 10px 20px;
    font-size: 13px;
  }
}

/* Additional styles for very small devices */
@media (max-width: 320px) {
  .internship-page {
    padding: 10px 5px;
  }

  .internship-title {
    font-size: 22px;
  }

  .internship-subtitle {
    font-size: 13px;
  }

  .section-title {
    font-size: 18px;
  }

  .benefit-card {
    padding: 12px;
  }

  .benefit-icon {
    font-size: 28px;
  }

  .benefit-card h4 {
    font-size: 16px;
  }

  .benefit-card p {
    font-size: 12px;
  }

  .timeline-content {
    padding: 12px;
  }

  .timeline-content h4 {
    font-size: 15px;
  }

  .timeline-content p {
    font-size: 12px;
  }

  .testimonial-card {
    padding: 15px;
  }

  .testimonial-content p {
    font-size: 13px;
  }

  .internship-item {
    padding: 12px;
  }

  .internship-image {
    height: 120px;
  }

  .btn-apply {
    padding: 8px 16px;
    font-size: 12px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .internship-page-header::before,
  .internship-page-header::after,
  .internship-title {
    animation: none;
  }
  
  .internship-title,
  .internship-subtitle {
    opacity: 1;
    transform: none;
  }
}

/* Header section */
/* ... existing code ... */

/* New sections styling */

/* Benefits Section */
.internship-benefits {
  margin: 60px 0;
  padding: 0 20px;
}

.benefits-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  font-size: clamp(24px, 4vw, 36px);
  color: #fdfafa;
  margin-bottom: 40px;
  position: relative;
  animation: slideInFromBottom 0.8s ease-out;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #87ceeb, #20b2aa);
  border-radius: 2px;
  animation: expandLine 1s ease-out 0.5s both;
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandLine {
  from { width: 0; }
  to { width: 60px; }
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 50px;
  opacity: 0;
  animation: fadeInUp 1s ease-out 0.3s forwards;
}

.benefit-card {
  background: rgba(24, 23, 24, 0.8);
  padding: 30px;
  border-radius: 16px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: translateY(50px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.benefit-card:nth-child(1) { animation-delay: 0.1s; }
.benefit-card:nth-child(2) { animation-delay: 0.2s; }
.benefit-card:nth-child(3) { animation-delay: 0.3s; }
.benefit-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.benefit-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.benefit-icon {
  font-size: 48px;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
  transition: transform 0.3s ease;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.benefit-card h4 {
  color: #87ceeb;
  font-size: 24px;
  margin-bottom: 15px;
  transition: color 0.3s ease;
}

.benefit-card:hover h4 {
  color: #20b2aa;
}

.benefit-card p {
  color: #cccccc;
  line-height: 1.6;
  font-size: 16px;
}

/* Process Section */
.internship-process {
  margin: 80px 0;
  padding: 50px 20px;
  background: linear-gradient(135deg, #10123e, #412548  ,#203343);
  border-radius: 30px;
}

.process-container {
  max-width: 1000px;
  margin: 0 auto;
}

.process-timeline {
  position: relative;
  margin-top: 60px;
  opacity: 0;
  animation: fadeIn 1s ease-out 0.7s forwards;
}

.process-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #87ceeb, #20b2aa);
  animation: drawLine 2s ease-out;
}

@keyframes drawLine {
  from { height: 0; }
  to { height: 100%; }
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 60px;
  position: relative;
  opacity: 0;
  animation: slideInUp 0.5s ease-out forwards;
}

.timeline-item:nth-child(1) { animation-delay: 0.8s; }
.timeline-item:nth-child(2) { animation-delay: 0.9s; }
.timeline-item:nth-child(3) { animation-delay: 1s; }
.timeline-item:nth-child(4) { animation-delay: 1.1s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
  animation-name: slideInTimelineRight;
}

@keyframes slideInTimelineRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.timeline-dot {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #87ceeb, #20b2aa);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3);
  animation: pulse 2s infinite;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

@keyframes pulse {
  0% { box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3); }
  50% { box-shadow: 0 4px 25px rgba(135, 206, 235, 0.6); }
  100% { box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3); }
}

.timeline-content {
  background: rgba(24, 23, 24, 0.9);
  padding: 30px;
  border-radius: 12px;
  width: 45%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(135, 206, 235, 0.2);
}

.timeline-content h4 {
  color: #87ceeb;
  font-size: 20px;
  margin-bottom: 10px;
}

.timeline-content p {
  color: #cccccc;
  line-height: 1.6;
}

/* Testimonials Section */
.internship-testimonials {
  margin: 80px 0;
  padding: 60px 20px;
  background: rgba(63, 34, 84, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 30px;
}

.testimonials-container {
  max-width: 800px;
  margin: 0 auto;
}

.testimonials-slider {
  margin-top: 50px;
  position: relative;
  overflow: hidden;
  opacity: 0;
  animation: fadeIn 1s ease-out 1.2s forwards;
}

.testimonial-card {
  background: rgba(24, 23, 24, 0.9);
  padding: 40px;
  border-radius: 16px;
  text-align: center;
  opacity: 0;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  position: absolute;
  width: 100%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(135, 206, 235, 0.2);
}

.testimonial-card.active {
  opacity: 1;
  transform: translateX(0);
  position: relative;
}

.testimonial-content p {
  color: #fdfafa;
  font-size: clamp(16px, 3vw, 20px);
  font-style: italic;
  line-height: 1.8;
  margin-bottom: 30px;
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, #87ceeb, #20b2aa);
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { box-shadow: 0 0 20px rgba(135, 206, 235, 0.3); }
  50% { box-shadow: 0 0 30px rgba(135, 206, 235, 0.6); }
}

.author-info {
  text-align: left;
}

.author-info h5 {
  color: #87ceeb;
  font-size: 18px;
  margin-bottom: 5px;
}

.author-info span {
  color: #b1b0b0;
  font-size: 14px;
}

/* Grid container */
.internship-list-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
  gap: clamp(16px, 3vw, 32px);
  padding: 0 10px;
  opacity: 0;
  animation: fadeIn 1s ease-out 1.4s forwards;
}

/* Individual internship cards */
.internship-item {
  background:  rgba(81, 69, 126, 0.05);
  border-radius: 20px;
  padding: 25px 25px;
  max-width: 450px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInCard 0.6s ease-out forwards;
  position: relative;
  overflow: hidden;
}

.internship-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 90%;
  height: 90%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.internship-item:hover::before {
  left: 100%;
}

@keyframes slideInCard {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.internship-item:nth-child(1) { animation-delay: 0.1s; }
.internship-item:nth-child(2) { animation-delay: 0.2s; }
.internship-item:nth-child(3) { animation-delay: 0.3s; }
.internship-item:nth-child(4) { animation-delay: 0.4s; }
.internship-item:nth-child(5) { animation-delay: 0.5s; }
.internship-item:nth-child(6) { animation-delay: 0.6s; }

.internship-item:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* Image styling */
.internship-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 12px;
  margin-bottom: 20px;
  transition: transform 0.3s ease;
  overflow: hidden;
  opacity: 0;
  animation: imageLoad 0.8s ease-out forwards;
}

.internship-item:hover .internship-image {
  transform: scale(1.05);
}

/* Text content */
.internship-name {
  font-size: clamp(18px, 4vw, 24px);
  margin: 15px 0;
  color: #fdfafa;
  font-weight: 600;
  transition: color 0.3s ease;
}

.internship-item:hover .internship-name {
  color: #87ceeb;
}

.internship-description {
  font-size: clamp(14px, 3vw, 16px);
  color: #cccccc;
  margin-bottom: 20px;
  line-height: 1.6;
  transition: color 0.3s ease;
}

.internship-item:hover .internship-description {
  color: #ffffff;
}

/* Apply button */
.btn-apply {
  padding: 12px 24px;
  background: linear-gradient(45deg, rgb(58, 48, 48), rgb(17, 13, 13), rgb(113, 19, 113));
  color: aqua;
  text-decoration: none;
  border-radius: 8px;
  display: inline-block;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.btn-apply::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(135, 206, 235, 0.3), transparent);
  transition: left 0.4s ease;
}

.btn-apply:hover::before {
  left: 100%;
}

.btn-apply:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  background: linear-gradient(45deg, rgb(78, 68, 68), rgb(37, 33, 33), rgb(153, 39, 153));
}

/* Loading animation */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.internship-item:nth-child(none):hover {
  animation:  0.6s ease-in-out;
}

/* Smooth scrolling behavior */
html {
  scroll-behavior: smooth;
}

/* Section Animations */

/* Header Section Animation */
.internship-page-header {
  animation: fadeInDown 1s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Benefits Section Animation */
.benefits-grid {
  opacity: 0;
  animation: fadeInUp 1s ease-out 0.3s forwards;
}

.benefit-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Requirements Section Animation */
.requirements-content {
  opacity: 0;
  animation: fadeInLeft 1s ease-out 0.5s forwards;
}

.requirements-list li {
  opacity: 0;
  animation: slideInRight 0.5s ease-out forwards;
}

.requirements-list li:nth-child(1) { animation-delay: 0.6s; }
.requirements-list li:nth-child(2) { animation-delay: 0.7s; }
.requirements-list li:nth-child(3) { animation-delay: 0.8s; }
.requirements-list li:nth-child(4) { animation-delay: 0.9s; }
.requirements-list li:nth-child(5) { animation-delay: 1s; }

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Process Timeline Animation */
.process-timeline {
  opacity: 0;
  animation: fadeIn 1s ease-out 0.7s forwards;
}

.timeline-item {
  opacity: 0;
  animation: slideInUp 0.5s ease-out forwards;
}

.timeline-item:nth-child(1) { animation-delay: 0.8s; }
.timeline-item:nth-child(2) { animation-delay: 0.9s; }
.timeline-item:nth-child(3) { animation-delay: 1s; }
.timeline-item:nth-child(4) { animation-delay: 1.1s; }

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Testimonials Animation */
.testimonials-slider {
  opacity: 0;
  animation: fadeIn 1s ease-out 1.2s forwards;
}

.testimonial-card {
  transition: transform 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
}

/* Internship List Animation */
.internship-list-wrapper {
  opacity: 0;
  animation: fadeIn 1s ease-out 1.4s forwards;
}

.internship-item {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.internship-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* Responsive Animations */
@media (max-width: 768px) {
  .benefits-grid,
  .requirements-content,
  .process-timeline,
  .testimonials-slider,
  .internship-list-wrapper {
    animation: fadeIn 0.8s ease-out forwards;
  }
  
  .requirements-list li,
  .timeline-item {
    animation: fadeIn 0.5s ease-out forwards;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation: none !important;
    transition: none !important;
  }
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced Hover Effects */
.btn-apply {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.btn-apply:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Image Hover Effects */
.internship-image {
  transition: transform 0.3s ease;
}

.internship-item:hover .internship-image {
  transform: scale(1.05);
}

/* Timeline Dot Animation */
.timeline-dot {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.timeline-item:hover .timeline-dot {
  transform: scale(1.2);
  box-shadow: 0 0 20px rgba(135, 206, 235, 0.4);
}

/* Benefit Icon Animation */
.benefit-icon {
  transition: transform 0.3s ease;
}

.benefit-card:hover .benefit-icon {
  transform: scale(1.2);
}

/* Section Title Animation */
.section-title {
  opacity: 0;
  animation: fadeInDown 0.8s ease-out forwards;
}

.section-title::after {
  animation: expandLine 1s ease-out 0.5s forwards;
}

@keyframes expandLine {
  from { width: 0; }
  to { width: 60px; }
}

/* Floating Elements Animation */
.floating-elements {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
}

/* Loading Animation for Images */
.internship-image {
  opacity: 0;
  animation: imageLoad 0.8s ease-out forwards;
}

@keyframes imageLoad {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

</style>

    <!-- Footer Button -->
    
  </section>
{% include "footer.html" %}
{% endblock  %}