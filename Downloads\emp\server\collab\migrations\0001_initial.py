# Generated by Django 5.1.2 on 2025-06-13 02:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Collab',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(max_length=50)),
                ('email', models.EmailField(max_length=50)),
                ('industry', models.Char<PERSON>ield(max_length=50)),
                ('description', models.TextField(max_length=200)),
                ('contact', models.IntegerField(max_length=15)),
            ],
        ),
        migrations.CreateModel(
            name='ContactUs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON><PERSON>(max_length=50)),
                ('email', models.EmailField(max_length=50)),
                ('contact', models.IntegerField(max_length=100)),
                ('subject', models.Char<PERSON>ield(max_length=50)),
                ('message', models.TextField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Partners',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='partner', to='collab.collab')),
            ],
        ),
    ]
