{% extends 'base.html' %}
{% block title %}
Admin <PERSON>
{% endblock title %}
{% block content %}
<style>
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

  body {
    margin: 0;
    font-family: 'Poppins', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
  }
  
  .form-cont {
    background: #1e2b3a;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6);
    width: 30%;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: fadeIn 0.5s ease-in-out;
    transition: box-shadow 0.5s ease;
    position: relative;
  }

  .close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    font-size: 2rem;
    font-weight: 700;
    color: #ccc;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
  }

  .close-btn:hover {
    color: #f55;
  }

  .form-cont:hover {
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.6);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .form-header {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
    color: white;
  }

  input.form-control,
  textarea.form-control,
  select.form-control {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 10px;
    border: 1px solid #34495e;
    border-radius: 8px;
    width: 100%;
    margin-bottom: 15px;
  }

  input.form-control:focus,
  select.form-control:focus,
  textarea.form-control:focus {
    outline: none;
    border-color: #38bdf8;
    box-shadow: 0 0 6px 2px rgba(56, 189, 248, 0.4);
    background-color: #34495e;
  }

  .form-label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #e0e6f1;
  }

  form {
    width: 100%;
    padding: 0 20px;
  }

  .option-group {
    display: flex;
    justify-content: space-between;
    width: 100%;
    border: 1px solid #384e63;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
  }

  .option-group input[type="radio"] {
    display: none;
  }

  .option-group label {
    flex: 1;
    text-align: center;
    padding: 8px 6px;
    font-weight: 600;
    background-color: #2a2f3b;
    color: #f1e4e0;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
    user-select: none;
  }

  .option-group label:not(:last-child) {
    border-right: 1px solid #384e63;
  }

  .option-group label:hover {
    background-color: #38bdf8;
    color: white;
  }

  .option-group input[type="radio"]:checked + label {
    background-color: #0ea5e9;
    color: white;
  }

  .button-row {
  
    gap: 8px;
    width: 100%;
    margin-top: 20px;
  }

  .submit {
    background-color: #0ea5e9;
    border: 1px solid #0ea5e9;
    color: white;
    width: 100%;
    border-radius: 8px;
    padding: 12px;
    font-weight: 600;
    margin-top:10px;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  
  .submit:hover, .cancel:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-5px);
  }

  

  .remove-characteristic {
    color: #94a3b8;
    cursor: pointer;
    font-size: 0.8rem;
    padding: 2px;
    transition: color 0.3s ease;
  }

  .remove-characteristic:hover {
    color: #e11d48;
  }

  @media (max-width: 768px) {
    .form-cont {
      width: 90%;
      padding: 20px;
    }
    .form-header {
      font-size: 1.5rem;
    }
    .submit, .cancel {
      width: 100%;
      margin-top: 10px;
    }
    .button-row {
      flex-direction: column;
      gap: 10px;
    }
  }
  .below{
    font-size: 0.8rem;
    color :gray;
    text-align: center;
    margin-top: 10px;
  }
</style>

<div class="container form-cont">
  <!-- Cross button -->
  <button type="button" class="close-btn" aria-label="Close form" onclick="history.back()">&times;</button>


  <div class="form-header">Admin Register</div>
  <form method="post">
    {% csrf_token %}
    <div class="row">
      <div class="mb-3 col-md-12">
        <label class="form-label">Username</label>
        <input type="text" name="username"  class="form-control" required>
      </div>
    </div>

    <div class="mb-3">
      <div class="mb-3 col-md-12">
        <label class="form-label">Passowrd</label>
        <input type="password" name="password"  class="form-control" required>
      </div>

    </div>

    <div class="button-row mt-3">
      <button type="submit" class="submit">Login</button>
    </div>
    <h6 class="below">Don't have an account ? <a href="{%url 'register'%}">Register</a></h6>
  </form>
</div>

{% endblock %} 