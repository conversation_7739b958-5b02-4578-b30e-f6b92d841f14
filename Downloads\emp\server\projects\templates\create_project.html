{% extends 'base.html' %}
{% block title %}
Add project
{% endblock title %}
{% block content %}
<style>
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

  body {
    margin: 0;
    font-family: 'Poppins', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
  }
  
  .form-cont {
    position: relative;
    background: #1e2b3a;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6);
    width: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: fadeIn 0.5s ease-in-out;
    transition: box-shadow 0.5s ease;
  }

  .form-cont:hover {
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.6);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .form-header {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
    color: white;
  }

  .form-label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #e0e6f1;
  }

  input.form-control,
  textarea.form-control,
  select.form-control {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 10px;
    border: 1px solid #34495e;
    border-radius: 8px;
    width: 100%;
    margin-bottom: 15px;
  }

  input.form-control:focus,
  select.form-control:focus,
  textarea.form-control:focus {
    outline: none;
    border-color: #38bdf8;
    box-shadow: 0 0 6px 2px rgba(56, 189, 248, 0.4);
    background-color: #34495e;
  }

  form {
    width: 100%;
    padding: 0 20px;
  }

  .button-row {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    width: 100%;
    margin-top: 20px;
  }

  .submit {
    background-color: #0ea5e9;
    border: 1px solid #0ea5e9;
    color: white;
    width: 48%;
    border-radius: 8px;
    padding: 12px;
    font-weight: 600;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .cancel {
    background-color: #dc2626;
    border: 1px solid #dc2626;
    color: white;
    width: 48%;
    border-radius: 8px;
    padding: 12px;
    font-weight: 600;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .submit:hover, .cancel:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-5px);
  }

  @media (max-width: 768px) {
    .form-cont {
      width: 90%;
      padding: 20px;
    }
    .form-header {
      font-size: 1.5rem;
    }
    .submit, .cancel {
      width: 100%;
      margin-top: 10px;
    }
    .button-row {
      flex-direction: column;
      gap: 10px;
    }
  }

  .close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    font-size: 2rem;
    font-weight: 700;
    color: #ccc;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
  }

  .close-btn:hover {
    color: #f55;
  }
  .container{
    margin-top: 100px;
  }
</style>

<div class="container mt-5 form-cont">
  <button type="button" class="close-btn" aria-label="Close form">&times;</button>

  <div class="form-header">Create New Project</div>

  <form method="post">
    {% csrf_token %}
    
    <div class="row">
      <div class="mb-3 col-md-6">
        <label class="form-label">Project Name</label>
        <input type="text" name="name" class="form-control" required>
      </div>
      <div class="mb-3 col-md-6">
        <label class="form-label">Company Name</label>
        <input type="text" name="company_name" class="form-control" required>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-md-6">
        <label class="form-label">Email</label>
        <input type="email" name="email" class="form-control" required>
      </div>
      <div class="mb-3 col-md-6">
        <label class="form-label">Contact</label>
        <input type="number" name="contact" class="form-control" required>
      </div>
    </div>

    <div class="mb-3">
      <label class="form-label">Description</label>
      <textarea name="description" class="form-control" rows="3" required></textarea>
    </div>

    <div class="button-row">
      <button type="submit" class="submit">Add Project</button>
      <button type="button" class="cancel" onclick="window.history.back();">Cancel</button>
    </div>
  </form>
</div>

<script>
  document.querySelector('.close-btn').addEventListener('click', () => {
    window.history.back();
  });
</script>
{% endblock %} 