{% extends "header.html" %}
{% block content %}
<style>
        .webdev-container {
            min-height: 100vh;
            padding: 80px 20px;
            background: linear-gradient(135deg, #0a192f 0%, #112240 100%);
            position: relative;
            overflow: hidden;
        }

        .webdev-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(100, 255, 218, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(100, 255, 218, 0.1) 0%, transparent 50%);
            animation: backgroundPulse 15s ease-in-out infinite alternate;
            z-index: 0;
        }

        @keyframes backgroundPulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.1); }
        }

        .webdev-contact-section {
            background: rgba(17, 34, 64, 0.75);
            border-radius: 20px;
            padding: 40px;
            padding-top: 100px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            backdrop-filter: blur(12px);
            border: 1px solid rgba(100, 255, 218, 0.15);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            animation: formSlideIn 0.8s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        @keyframes formSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .webdev-contact-header {
            text-align: center;
            margin-bottom: 40px;
            animation: headerFadeIn 1s ease-out 0.3s forwards;
            opacity: 0;
        }

        @keyframes headerFadeIn {
            to {
                opacity: 1;
            }
        }

        .webdev-contact-header h1 {
            font-size: 2.5rem;
            color: #64ffda;
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
        }

        .webdev-contact-header h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background: #64ffda;
            animation: lineExpand 1s ease-out 0.8s forwards;
        }

        @keyframes lineExpand {
            to {
                width: 100px;
            }
        }

        .webdev-contact-header p {
            color: #8892b0;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .webdev-form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            margin-bottom: 25px;
            animation: gridFadeIn 1s ease-out 0.5s forwards;
            opacity: 0;
        }

        @keyframes gridFadeIn {
            to {
                opacity: 1;
            }
        }

        .webdev-form-group {
            position: relative;
            animation: formGroupSlideIn 0.5s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        .webdev-form-group:nth-child(1) { animation-delay: 0.6s; }
        .webdev-form-group:nth-child(2) { animation-delay: 0.7s; }
        .webdev-form-group:nth-child(3) { animation-delay: 0.8s; }
        .webdev-form-group:nth-child(4) { animation-delay: 0.9s; }
        .webdev-form-group:nth-child(5) { animation-delay: 1s; }
        .webdev-form-group:nth-child(6) { animation-delay: 1.1s; }

        @keyframes formGroupSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .webdev-form-group label {
            display: block;
            margin-bottom: 8px;
            color: #64ffda;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .webdev-form-group input,
        .webdev-form-group textarea {
            width: 100%;
            padding: 15px;
            border: 1px solid rgba(100, 255, 218, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .webdev-form-group input:focus,
        .webdev-form-group textarea:focus {
            outline: none;
            border-color: #64ffda;
            box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.2);
            transform: translateY(-2px);
        }

        .webdev-form-group input::placeholder,
        .webdev-form-group textarea::placeholder {
            color: rgba(136, 146, 176, 0.7);
        }

        .webdev-form-group.full-width {
            grid-column: 1 / -1;
            animation-delay: 1.2s;
        }

        .webdev-submit-btn {
            background: #64ffda;
            color: #0a192f;
            border: none;
            padding: 15px 40px;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 30px;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
            animation: buttonFadeIn 1s ease-out 1.3s forwards;
            opacity: 0;
        }

        .webdev-submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .webdev-submit-btn:hover::before {
            left: 100%;
        }

        .webdev-submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(100, 255, 218, 0.2);
        }

        @keyframes buttonFadeIn {
            to {
                opacity: 1;
            }
        }

        /* Responsive Styles */
        @media (max-width: 1200px) {
            .webdev-container {
                padding: 60px 20px;
            }
        }

        @media (max-width: 992px) {
            .webdev-form-grid {
                grid-template-columns: 1fr;
            }

            .webdev-contact-section {
                padding: 30px 20px;
                padding-top: 80px;
            }
        }

        @media (max-width: 768px) {
            .webdev-contact-header h1 {
                font-size: 2rem;
            }

            .webdev-contact-section {
                padding: 25px 15px;
                padding-top: 60px;
            }

            .webdev-submit-btn {
                padding: 12px 30px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .webdev-container {
                padding: 40px 10px;
            }

            .webdev-contact-header h1 {
                font-size: 1.8rem;
            }

            .webdev-form-group input,
            .webdev-form-group textarea {
                padding: 12px;
            }

            .webdev-submit-btn {
                width: 100%;
            }
        }

        /* Reduced Motion */
        @media (prefers-reduced-motion: reduce) {
            .webdev-container::before,
            .webdev-contact-section,
            .webdev-contact-header,
            .webdev-form-grid,
            .webdev-form-group,
            .webdev-submit-btn {
                animation: none !important;
                transition: none !important;
            }
        }

        /* Add these styles to your existing CSS */
        .readonly-field {
            background: rgba(100, 255, 218, 0.1) !important;
            cursor: not-allowed !important;
            border-color: rgba(100, 255, 218, 0.3) !important;
            position: relative;
        }

        .readonly-field::after {
            content: 'Pre-selected';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8rem;
            color: #64ffda;
            background: rgba(100, 255, 218, 0.1);
            padding: 2px 8px;
            border-radius: 4px;
        }

        .readonly-field:focus {
            transform: none !important;
            box-shadow: none !important;
        }

        .webdev-form-group label[for="webdev-service"],
        .webdev-form-group label[for="webdev-budget"] {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .webdev-form-group label[for="webdev-service"]::after,
        .webdev-form-group label[for="webdev-budget"]::after {
            content: '(Pre-selected)';
            font-size: 0.8rem;
            color: #64ffda;
            opacity: 0.8;
        }
</style>
<div class="webdev-container">
    <div class="webdev-contact-section">
        <div class="webdev-contact-header" id="webdev-contact">
            <h1>Request Our Services</h1>
            <p>Fill out the form below and we'll get back to you within 24 hours to discuss your project requirements.</p>
        </div>
        <form class="webdev-contact-form" action="#" method="POST">
            {% csrf_token %}
            <div class="webdev-form-grid">
                <div class="webdev-form-group">
                    <label for="webdev-name">Full Name</label>
                    <input type="text" id="webdev-name" name="name" required placeholder="Enter your fullname">
                </div>
                <div class="webdev-form-group">
                    <label for="webdev-email">Email Address</label>
                    <input type="email" id="webdev-email" name="email" required placeholder="Enter your Email">
                </div>
                <div class="webdev-form-group">
                    <label for="webdev-phone">Phone Number</label>
                    <input type="tel" id="webdev-phone" name="contact" required placeholder="Enter your contact number">
                </div>
                <div class="webdev-form-group">
                    <label for="webdev-company">Company Name</label>
                    <input type="text" id="webdev-company" name="company_name" placeholder="Enter your company full name">
                </div>
                <div class="webdev-form-group">
                    <label for="webdev-service">Service Required</label>
                    <input type="text" id="webdev-service" name="service_name" value="{{service.service_name}}" required readonly class="readonly-field">
                </div>
                <div class="webdev-form-group">
                    <label for="webdev-budget">Budget Range (in Rs)</label>
                    <input type="number" id="webdev-budget" name="price" value="{{service.price}}" required readonly class="readonly-field">
                </div>
                
            </div
            ><div class="webdev-form-group full-width">
                    <label for="webdev-message">Project Details</label>
                    <textarea id="webdev-message" name="description" required placeholder="Please describe your project requirements, goals, and any specific features you'd like to include..."></textarea>
                </div>
            <div style="display: flex; justify-content: center;">
                <button type="submit" class="webdev-submit-btn">Submit Request</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}