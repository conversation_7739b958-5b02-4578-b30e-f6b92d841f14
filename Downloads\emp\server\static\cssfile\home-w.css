/* Navbar styling with static position, animations, and responsiveness */
.navbar {
  background: linear-gradient(90deg, #080f1c, #101d36);
  position: fixed;
  width: 100%;
  padding: 15px 0;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.navbar.scrolled {
  padding: 10px 0;
  background: rgba(8, 15, 28, 0.95);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.navbar-brand {
  position: relative;
  display: inline-block;
  padding: 0;
  overflow: hidden;
}

.navbar-brand img {
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
}

.navbar-brand::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.navbar-brand:hover::after {
  left: 100%;
}

.navbar-brand:hover img {
  transform: scale(1.05);
}

/* Animated hamburger toggle */
.navbar-toggler {
  border: none;
  background: transparent;
  padding: 5px;
  width: 40px;
  height: 40px;
  position: relative;
  transition: all 0.3s ease;
  outline: none;
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 3px rgba(72, 202, 228, 0.4);
}

.navbar-toggler-icon {
  background-image: none !important;
  position: relative;
  transition: all 0.3s ease;
}

.navbar-toggler-icon::before,
.navbar-toggler-icon::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: #fff;
  left: 0;
  transition: all 0.3s ease;
}

.navbar-toggler-icon::before {
  top: -8px;
}

.navbar-toggler-icon::after {
  bottom: -8px;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 24px;
  height: 2px;
  background-color: #fff;
  vertical-align: middle;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon {
  background-color: transparent;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::before {
  transform: rotate(45deg);
  top: 0;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::after {
  transform: rotate(-45deg);
  bottom: 0;
}

/* Navbar items animation */
.navbar-nav .nav-item {
  position: relative;
  margin: 0 5px;
  opacity: 0;
  transform: translateY(-20px);
  animation: navItemFade 0.5s ease forwards;
}

@keyframes navItemFade {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.navbar-nav .nav-item:nth-child(1) { animation-delay: 0.1s; }
.navbar-nav .nav-item:nth-child(2) { animation-delay: 0.2s; }
.navbar-nav .nav-item:nth-child(3) { animation-delay: 0.3s; }
.navbar-nav .nav-item:nth-child(4) { animation-delay: 0.4s; }
.navbar-nav .nav-item:nth-child(5) { animation-delay: 0.5s; }
.navbar-nav .nav-item:nth-child(6) { animation-delay: 0.6s; }

.nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500;
  padding: 10px 15px !important;
  position: relative;
  transition: all 0.3s ease;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15px;
  right: 15px;
  height: 2px;
  background: linear-gradient(90deg, #48cae4, #7b2cbf);
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 2px;
}

.nav-link:hover {
  color: #ffffff !important;
  transform: translateY(-2px);
}

.nav-link:hover::before {
  transform: scaleX(1);
}

.nav-link.active {
  color: #ffffff !important;
  font-weight: 600;
}

.nav-link.active::before {
  transform: scaleX(1);
  background: linear-gradient(90deg, #48cae4, #7b2cbf);
}

/* Search form */
.search-form {
  position: relative;
  max-width: 300px;
  width: 100%;
  animation: slideIn 0.5s ease-out 0.6s both;
  opacity: 0;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.search-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
  border-radius: 20px;
  padding: 8px 15px;
  transition: all 0.3s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-input:focus {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(72, 202, 228, 0.25);
  border-color: rgba(72, 202, 228, 0.5);
  color: #fff;
}

.search-form .btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-form .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
  z-index: -1;
}

.search-form .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.search-form .btn:hover::before {
  left: 100%;
}

.search-form .btn:active {
  transform: translateY(-1px);
}

/* Collapse animation */
.collapse.navbar-collapse {
  transition: all 0.4s ease;
}

.collapsing {
  transition: all 0.3s ease !important;
}

/* Enhanced Navbar Responsive Styles */
@media (max-width: 992px) {
  .navbar {
    padding: 10px 0;
    background: linear-gradient(90deg, #080f1c, #101d36);
  }

  .navbar-collapse {
    background: rgba(8, 15, 28, 0.98);
    backdrop-filter: blur(10px);
    padding: 20px;
    margin-top: 15px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-height: 85vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .navbar-nav {
    padding: 10px 0;
    margin-bottom: 15px;
  }

  .nav-item {
    margin: 5px 0;
    opacity: 1;
    transform: none;
    animation: none;
  }

  .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 1.1rem;
    padding: 12px 20px !important;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
    margin: 2px 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .nav-link::after {
    content: '›';
    font-size: 1.2rem;
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
    color: #fff !important;
  }

  .nav-link:hover::after {
    opacity: 1;
    transform: translateX(3px);
  }

  .nav-link.active {
    background: rgba(72, 202, 228, 0.1);
    color: #48cae4 !important;
  }

  .search-form {
    margin-top: 20px;
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .search-input {
    width: 100%;
    padding: 12px 20px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: #fff;
  }

  .search-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(72, 202, 228, 0.5);
    box-shadow: 0 0 0 3px rgba(72, 202, 228, 0.25);
  }

  .search-form .btn {
    width: 100%;
    padding: 12px 24px;
    font-size: 1rem;
    background: linear-gradient(90deg, #63fff5, #63a4ff);
    border: none;
    border-radius: 10px;
    color: #0a192f;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .search-form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(99, 255, 245, 0.2);
  }

  /* Navbar Toggler Improvements */
  .navbar-toggler {
    padding: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
  }

  .navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(72, 202, 228, 0.25);
  }

  .navbar-toggler-icon {
    width: 24px;
    height: 2px;
    background-color: #fff;
    position: relative;
    transition: all 0.3s ease;
  }

  .navbar-toggler-icon::before,
  .navbar-toggler-icon::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 2px;
    background-color: #fff;
    left: 0;
    transition: all 0.3s ease;
  }

  .navbar-toggler-icon::before {
    top: -8px;
  }

  .navbar-toggler-icon::after {
    bottom: -8px;
  }

  .navbar-toggler[aria-expanded="true"] .navbar-toggler-icon {
    background-color: transparent;
  }

  .navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::before {
    transform: rotate(45deg);
    top: 0;
  }

  .navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::after {
    transform: rotate(-45deg);
    bottom: 0;
  }
}

/* Small Mobile Devices */
@media (max-width: 576px) {
  .navbar {
    padding: 8px 0;
  }

  .navbar-brand img {
    height: 30px !important;
  }

  .navbar-collapse {
    padding: 15px;
    margin-top: 10px;
  }

  .nav-link {
    font-size: 1rem;
    padding: 10px 15px !important;
  }

  .search-form {
    margin-top: 15px;
    gap: 10px;
  }

  .search-input {
    padding: 10px 15px;
    font-size: 0.95rem;
  }

  .search-form .btn {
    padding: 10px 20px;
    font-size: 0.95rem;
  }

  .navbar-toggler {
    padding: 6px;
  }

  .navbar-toggler-icon {
    width: 20px;
  }

  .navbar-toggler-icon::before,
  .navbar-toggler-icon::after {
    width: 20px;
  }
}

/* Desktop hover effect for nav items */
@media (min-width: 993px) {
  .nav-item::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, rgba(72, 202, 228, 0), rgba(72, 202, 228, 0.5), rgba(72, 202, 228, 0));
    opacity: 0;
    transition: all 0.3s ease;
  }
  
  .nav-item:hover::after {
    opacity: 1;
    bottom: 0;
  }
}

/* Loading animation */
.navbar.loading .navbar-brand img {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}

/* Dark/light mode support */
@media (prefers-color-scheme: dark) {
  .navbar {
    background: linear-gradient(90deg, #050a14, #0b142a);
  }
}

@media (prefers-color-scheme: light) {
  .search-input {
    background: rgba(0, 0, 0, 0.05);
    color: #333;
  }
  
  .search-input::placeholder {
    color: rgba(0, 0, 0, 0.5);
  }
  
  .search-input:focus {
    background: rgba(0, 0, 0, 0.08);
    color: #333;
  }
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
   background: linear-gradient(90deg, #080f1c, #101d36);
    overflow: hidden;
    position: relative;
    padding: 40px 20px;
  }
  
  /* Animated background elements */
  .hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 20%, rgba(99, 255, 245, 0.05) 0%, transparent 30%),
      radial-gradient(circle at 80% 80%, rgba(232, 131, 255, 0.05) 0%, transparent 30%);
    animation: backgroundPulse 10s ease-in-out infinite alternate;
    z-index: 0;
  }
  
  @keyframes backgroundPulse {
    0% { transform: scale(1); opacity: 0.5; }
    100% { transform: scale(1.1); opacity: 0.8; }
  }
  
  .hero-content {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
  }
  
  /* Hero text section */
  .hero-text {
    flex: 1;
    padding-right: 40px;
    animation: slideInFromLeft 1.2s ease-out;
  }
  
  @keyframes slideInFromLeft {
    from {
      opacity: 0;
      transform: translateX(-100px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  .hero-text h1 {
    font-size: clamp(2rem, 5vw, 3rem);
    margin-bottom: 25px;
    color: #969595;
    font-weight: 800;
    line-height: 1.2;
    position: relative;
  }
  
  /* Animated company name */
  .animated-text {
    background: linear-gradient(90deg, #44f7eb, #d46bec);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: rgb(9, 183, 235);
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
    display: inline-block;
  }
  
  .animated-text span {
    display: inline-block;
    opacity: 1;
    transform: translateY(30px);
    animation: letterFallIn 2s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards infinite;
  }
  
  .animated-text span:nth-child(1) { animation-delay: 0.1s; }
  .animated-text span:nth-child(2) { animation-delay: 0.15s; }
  .animated-text span:nth-child(3) { animation-delay: 0.2s; }
  .animated-text span:nth-child(4) { animation-delay: 0.25s; }
  .animated-text span:nth-child(5) { animation-delay: 0.3s; }
  .animated-text span:nth-child(6) { animation-delay: 0.4s; }
  .animated-text span:nth-child(7) { animation-delay: 0.45s; }
  .animated-text span:nth-child(8) { animation-delay: 0.5s; }
  .animated-text span:nth-child(9) { animation-delay: 0.55s; }
  .animated-text span:nth-child(10) { animation-delay: 0.6s; }
  .animated-text span:nth-child(11) { animation-delay: 0.65s; }
  .animated-text span:nth-child(12) { animation-delay: 0.7s; }
  .animated-text span:nth-child(13) { animation-delay: 0.75s; }
  .animated-text span:nth-child(14) { animation-delay: 0.8s; }
  .animated-text span:nth-child(15) { animation-delay: 0.85s; }
  
  @keyframes letterFallIn {
    from {
      opacity: 1;
      transform: translateY(30px);
    }
    to {
      opacity: 2;
      transform: translateY(5px);
    }
  }
  
  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  .hero-text p {
    font-size: clamp(1rem, 2vw, 1.25rem);
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 35px;
    line-height: 1.7;
    max-width: 600px;
    animation: fadeIn 1s ease-out 0.5s both;
    opacity: 0;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  /* CTA Buttons */
  .cta-buttons {
    display: flex;
    gap: 20px;
    animation: slideUp 1s ease-out 0.7s both;
    opacity: 0;
  }
  
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .btn {
    padding: 14px 28px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
  }
  
  .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: left 0.5s ease;
    z-index: -1;
  }
  
  .btn:hover::before {
    left: 100%;
  }
  
  .btn-primary {
    background: linear-gradient(90deg, #63fff5, #63a4ff);
    color: #0a192f;
    box-shadow: 0 10px 20px rgba(99, 255, 245, 0.15);
  }
  
  .btn-primary:hover {
    background: linear-gradient(90deg, #5ae0d7, #5590e6);
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(99, 255, 245, 0.2);
  }
  
  .btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #63fff5;
  }
  
  .btn-secondary:hover {
    background: rgba(99, 255, 245, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(99, 255, 245, 0.1);
  }
  
  /* Hero image */
  .hero-image {
    flex: 1;
    position: relative;
    animation: floatIn 1.2s ease-out 0.3s both;
    opacity: 0;
    transform: translateY(50px);
  }
  
  @keyframes floatIn {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: floats 6s ease-in-out infinite;
  }
  
  @keyframes floats {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-20px); }
  }
  
  /* Services Section */
  .services-section {
    padding: 30px 20px;
    /* background: linear-gradient(90deg, #080f1c, #101d36); */
     background: linear-gradient(135deg, #161b2f 0%, #28163b 100%); 
    position: relative;
    overflow: hidden;
  }
  
  .services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(99, 255, 245, 0.05) 1px, transparent 1px),
      radial-gradient(circle at 75% 75%, rgba(232, 131, 255, 0.05) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: backgroundMove 20s linear infinite;
    z-index: 0;
  }
  .services-section .section-title::after{
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 4px;
    background: linear-gradient(135deg, #11ff00 0%, #fd042a 100%);
    border-radius: 2px;
    animation: titleUnderline 1.5s ease-out 0.5s both;
  }
  
  @keyframes backgroundMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
  }
  
  .section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    text-align: center;
    margin-bottom: 20px;
    color: #fff;
    font-weight: 700;
    position: relative;
    animation: titleSlideIn 1s ease-out;
  }
  
  @keyframes titleSlideIn {
    from {
      opacity: 0;
      transform: translateY(-30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 4px;
    background: linear-gradient(135deg, #161b2f 0%, #28163b 100%);
    border-radius: 2px;
    animation: titleUnderline 1.5s ease-out 0.5s both;
  }
  
  @keyframes titleUnderline {
    from { width: 0; }
    to { width: 100px; }
  }
  
  .section-subtitle {
    text-align: center;
    font-size: clamp(1rem, 2vw, 1.25rem);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 60px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    animation: subtitleFadeIn 1s ease-out 0.3s both;
    opacity: 0;
  }
  
  @keyframes subtitleFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Services grid */
  .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
  }
  
  .service-card {
    max-width: 500px;
    background: rgba(30, 25, 48, 0.05);
    backdrop-filter: blur(10px);
    border: 5px solid rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    padding: 30px;
    text-align: center;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    animation: cardFadeIn 0.8s ease-out both;
    opacity: 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  .service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.7s ease;
  }
  
  .service-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(99, 255, 245, 0.3);
  }
  
  .service-card:hover::before {
    left: 100%;
  }
  
  .service-card img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 15px;
    margin-bottom: 25px;
    transition: all 0.5s ease;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .service-card:hover img {
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    filter: brightness(1.1);
  }
  
  .service-card h3 {
    font-size: clamp(1.25rem, 3vw, 1.5rem);
    color: #fff;
    margin-bottom: 20px;
    font-weight: 600;
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
  }
  
  .service-card h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #63fff5, #e883ff);
    transition: width 0.4s ease;
  }
  
  .service-card:hover h3 {
    transform: translateY(-2px);
    color: #63fff5;
  }
  
  .service-card:hover h3::after {
    width: 80%;
  }
  
  .requirements-list {
    list-style: none;
    padding: 0;
    margin: 0 0 25px 0;
    text-align: left;
  }
  
  .requirements-list li {
    color: rgba(255, 255, 255, 0.8);
    padding: 8px 0;
    font-size: 0.95rem;
    position: relative;
    padding-left: 25px;
    transition: all 0.3s ease;
  }
  
  .requirements-list li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: #63fff5;
    transition: all 0.3s ease;
  }
  
  .service-card:hover .requirements-list li {
    transform: translateX(5px);
  }
  
  .service-card:hover .requirements-list li::before {
    color: #e883ff;
  }
  
  .apply-btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(90deg, #63fff5, #63a4ff);
    color: #0a192f !important;
    font-weight: 600;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    z-index: 1;
    box-shadow: 0 5px 15px rgba(99, 255, 245, 0.2);
  }
  
  .apply-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
    z-index: -1;
  }
  
  .apply-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(99, 255, 245, 0.3);
  }
  
  .apply-btn:hover::before {
    left: 100%;
  }
  
  /* Enhanced Responsive Styles for Service Cards */
  @media (max-width: 992px) {
    .service-card {
      max-width: 100%;
      padding: 25px;
    }
    
    .service-card img {
      height: 200px;
    }
  }
  
  @media (max-width: 768px) {
    .service-card {
      padding: 20px;
    }
    
    .service-card img {
      height: 180px;
      margin-bottom: 20px;
    }
    
    .service-card h3 {
      font-size: 1.3rem;
      margin-bottom: 15px;
    }
    
    .requirements-list li {
      font-size: 0.9rem;
      padding: 6px 0;
      padding-left: 22px;
    }
    
    .apply-btn {
      padding: 10px 25px;
      font-size: 0.95rem;
    }
  }
  
  @media (max-width: 576px) {
    .service-card {
      padding: 15px;
    }
    
    .service-card img {
      height: 160px;
      margin-bottom: 15px;
    }
    
    .service-card h3 {
      font-size: 1.2rem;
      margin-bottom: 12px;
    }
    
    .requirements-list li {
      font-size: 0.85rem;
      padding: 5px 0;
      padding-left: 20px;
    }
    
    .apply-btn {
      padding: 8px 20px;
      font-size: 0.9rem;
    }
  }
  
  /* Accessibility improvements for service cards */
  @media (prefers-reduced-motion: reduce) {
    .service-card,
    .service-card img,
    .service-card h3,
    .requirements-list li,
    .apply-btn {
      transition: none !important;
      animation: none !important;
      transform: none !important;
    }
    
    .service-card:hover {
      transform: none !important;
    }
    
    .service-card:hover img {
      transform: none !important;
    }
    
    .service-card:hover h3 {
      transform: none !important;
    }
    
    .service-card:hover .requirements-list li {
      transform: none !important;
    }
    
    .apply-btn:hover {
      transform: none !important;
    }
  }
  
  /* Internships Section */
  .internships-section {
    padding: 100px 20px;
    background: linear-gradient(135deg, #1f1339 0%, #1b0a2f 100%);
    position: relative;
    overflow: hidden;
  }
  
  .internships-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 30% 30%, rgba(99, 255, 245, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 70% 70%, rgba(232, 131, 255, 0.05) 0%, transparent 50%);
    animation: rotateBg 30s linear infinite;
    z-index: 0;
  }
  .internships-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 4px;
    background: linear-gradient(135deg, #11ff00 0%, #fd042a 100%);
    border-radius: 2px;
    animation: titleUnderline 1.5s ease-out 0.5s both;
  }
  
  @keyframes rotateBg {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .internships-section h2{
    color: #2fde10;
  }
  
  .internships-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
  }
  
  .internship-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 5px solid rgba(255, 255, 255, 0.1);
    max-width: 450px;
    border-radius: 30px;
    padding: 30px;
    text-align: center;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    animation: cardSlideUp 0.8s ease-out both;
    opacity: 0;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  }
  
  .internship-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.7s ease;
  }
  
  .internship-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(99, 255, 245, 0.3);
  }
  
  .internship-card:hover::before {
    left: 100%;
  }
  
  .internship-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: 20px;
    margin-bottom: 25px;
    transition: all 0.5s ease;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    filter: brightness(0.95);
  }
  
  .internship-card:hover img {
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
    filter: brightness(1.1);
  }
  
  .internship-card h3 {
    font-size: clamp(1.25rem, 3vw, 1.5rem);
    color: #2fde10;
    margin-bottom: 20px;
    font-weight: 600;
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
  }
  
  .internship-card h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #63fff5, #e883ff);
    transition: width 0.4s ease;
  }
  
  .internship-card:hover h3 {
    transform: translateY(-2px);
    color: #63fff5;
  }
  
  .internship-card:hover h3::after {
    width: 80%;
  }
  
  .internship-card p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 25px;
    transition: all 0.3s ease;
    position: relative;
    padding: 0 10px;
  }
  
  .internship-card:hover p {
    transform: translateY(-2px);
    color: rgba(255, 255, 255, 0.9);
  }
  
  .internship-card .apply-btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(90deg, #63fff5, #63a4ff);
    color: #0a192f !important;
    font-weight: 600;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    z-index: 1;
    box-shadow: 0 5px 15px rgba(99, 255, 245, 0.2);
  }
  
  .internship-card .apply-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
    z-index: -1;
  }
  
  .internship-card .apply-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(99, 255, 245, 0.3);
  }
  
  .internship-card .apply-btn:hover::before {
    left: 100%;
  }
  
  /* Staggered animation for internship cards */
  .internship-card:nth-child(1) { animation-delay: 0.1s; }
  .internship-card:nth-child(2) { animation-delay: 0.2s; }
  .internship-card:nth-child(3) { animation-delay: 0.3s; }
  .internship-card:nth-child(4) { animation-delay: 0.4s; }
  
  @keyframes cardSlideUp {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Enhanced Responsive Styles for Internship Cards */
  @media (max-width: 992px) {
    .internship-card {
      max-width: 100%;
      padding: 25px;
    }
    
    .internship-card img {
      height: 220px;
    }
  }
  
  @media (max-width: 768px) {
    .internship-card {
      padding: 20px;
    }
    
    .internship-card img {
      height: 200px;
      margin-bottom: 20px;
    }
    
    .internship-card h3 {
      font-size: 1.3rem;
      margin-bottom: 15px;
    }
    
    .internship-card p {
      font-size: 0.95rem;
      margin-bottom: 20px;
    }
    
    .internship-card .apply-btn {
      padding: 10px 25px;
      font-size: 0.95rem;
    }
  }
  
  @media (max-width: 576px) {
    .internship-card {
      padding: 15px;
    }
    
    .internship-card img {
      height: 180px;
      margin-bottom: 15px;
    }
    
    .internship-card h3 {
      font-size: 1.2rem;
      margin-bottom: 12px;
    }
    
    .internship-card p {
      font-size: 0.9rem;
      margin-bottom: 15px;
    }
    
    .internship-card .apply-btn {
      padding: 8px 20px;
      font-size: 0.9rem;
    }
  }
  
  /* Accessibility improvements for internship cards */
  @media (prefers-reduced-motion: reduce) {
    .internship-card,
    .internship-card img,
    .internship-card h3,
    .internship-card p,
    .internship-card .apply-btn {
      transition: none !important;
      animation: none !important;
      transform: none !important;
    }
    
    .internship-card:hover {
      transform: none !important;
    }
    
    .internship-card:hover img {
      transform: none !important;
    }
    
    .internship-card:hover h3 {
      transform: none !important;
    }
    
    .internship-card:hover p {
      transform: none !important;
    }
    
    .internship-card .apply-btn:hover {
      transform: none !important;
    }
  }
  
  /* Company Collaboration Section */
  .cd-company-collab-section {
    padding: 80px 20px;
    background: linear-gradient(45deg, #0a192f, #142b59);
    position: relative;
    overflow: hidden;
  }
  
  .cd-company-collab-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2363fff5' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    animation: patternMove 20s linear infinite;
    z-index: 0;
  }
  
  @keyframes patternMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
  }
  
  .cd-company-collab-container {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
  }
  
  .cd-company-collab-content {
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 60px 40px;
    animation: scaleIn 0.8s ease-out;
  }
  
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  .cd-company-collab-title {
    font-size: clamp(1.75rem, 4vw, 2.5rem);
    color: #fff;
    margin-bottom: 20px;
    font-weight: 700;
    position: relative;
    display: inline-block;
  }
  
  .cd-company-collab-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #63fff5, #e883ff);
    animation: expandWidth 1.5s ease-out 0.5s forwards;
  }
  
  @keyframes expandWidth {
    from { width: 0; }
    to { width: 80px; }
  }
  
  .cd-company-collab-description {
    font-size: clamp(1rem, 2vw, 1.25rem);
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    max-width: 700px;
    margin: 0 auto 35px auto;
  }
  
  .search-button {
    padding: 14px 30px;
    font-size: 1.1rem;
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(99, 255, 245, 0.4); }
    70% { box-shadow: 0 0 0 15px rgba(99, 255, 245, 0); }
    100% { box-shadow: 0 0 0 0 rgba(99, 255, 245, 0); }
  }
  
  /* Responsive Design */
  @media (max-width: 1024px) {
    .hero-content {
      flex-direction: column-reverse;
      padding: 20px;
      text-align: center;
    }
    
    .hero-text {
      padding-right: 0;
      margin-top: 40px;
    }
    
    .hero-text p {
      margin-left: auto;
      margin-right: auto;
    }
    
    .cta-buttons {
      justify-content: center;
    }
    
    .hero-image {
      max-width: 80%;
      margin: 0 auto;
    }
    
    .services-grid,
    .internships-grid {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 25px;
    }
    
    .cd-company-collab-content {
      padding: 50px 30px;
    }
  }
  
  @media (max-width: 768px) {
    .hero {
      padding: 100px 20px 60px;
    }
    
    .hero-content {
      gap: 30px;
    }
    
    .hero-image {
      max-width: 100%;
    }
    
    .cta-buttons {
      flex-direction: column;
      gap: 15px;
      width: 100%;
      max-width: 300px;
      margin: 0 auto;
    }
    
    .btn {
      width: 100%;
      text-align: center;
    }
    
    .services-section,
    .internships-section {
      padding: 70px 20px;
    }
    
    .service-card,
    .internship-card {
      padding: 25px;
    }
    
    .service-card img,
    .internship-card img {
      height: 180px;
    }
    
    .cd-company-collab-section {
      padding: 60px 20px;
    }
  }
  
  @media (max-width: 480px) {
    .hero {
      padding: 80px 15px 50px;
    }
    
    .hero-text h1 {
      font-size: clamp(1.6rem, 7vw, 2.2rem);
    }
    
    .services-section,
    .internships-section {
      padding: 50px 15px;
    }
    
    .section-title,
    .cd-company-collab-title {
      margin-bottom: 15px;
    }
    
    .section-subtitle,
    .cd-company-collab-description {
      margin-bottom: 30px;
    }
    
    .services-grid,
    .internships-grid {
      margin-top: 40px;
      gap: 20px;
    }
    
    .service-card,
    .internship-card {
      padding: 20px;
    }
    
    .service-card img,
    .internship-card img {
      height: 160px;
      margin-bottom: 20px;
    }
    
    .cd-company-collab-content {
      padding: 35px 20px;
    }
  }
  
  /* Prefers reduced motion */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
    
    .hero::before,
    .services-section::before,
    .internships-section::before,
    .cd-company-collab-section::before,
    .hero-image img,
    .apply-btn,
    .search-button {
      animation: none !important;
    }
  }
  
  /* Dark theme preferences */
  @media (prefers-color-scheme: dark) {
    .service-card,
    .internship-card,
    .cd-company-collab-content {
      background: linear-gradient(to right,rgb(44, 58, 78) ,rgb(16, 24, 93));
    }
  }
  
  /* Print styles */
  @media print {
    .hero,
    .services-section,
    .internships-section,
    .cd-company-collab-section {
      background: white !important;
      color: black !important;
      padding: 20px !important;
    }
    
    .hero-text h1,
    .section-title,
    .cd-company-collab-title {
      color: #000 !important;
    }
    
    .hero-text p,
    .section-subtitle,
    .service-card p,
    .internship-card p,
    .cd-company-collab-description {
      color: #333 !important;
    }
    
    .cta-buttons,
    .apply-btn,
    .search-button {
      display: none !important;
    }
    
    .service-card,
    .internship-card {
      break-inside: avoid;
      border: 1px solid #ddd !important;
      background: white !important;
    }
    
    .service-card h3,
    .internship-card h3 {
      color: #000 !important;
    }
  }

/* Modern, responsive footer with animations */
.site-footer {
  background: linear-gradient(135deg, #080f1c, #101d36);
  color: #f8f9fa;
  padding: 60px 0 20px;
  position: relative;
  overflow: hidden;
  font-family: 'Poppins', sans-serif;
}

/* Animated background elements */
.site-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(72, 202, 228, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(123, 44, 191, 0.05) 0%, transparent 50%);
  animation: backgroundShift 15s ease-in-out infinite alternate;
  z-index: 0;
}

@keyframes backgroundShift {
  0% { transform: scale(1) rotate(0deg); }
  100% { transform: scale(1.05) rotate(3deg); }
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

/* Footer sections with staggered animation */
.footer-about, 
.footer-links, 
.footer-contact {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.footer-about { animation-delay: 0.1s; }
.footer-links { animation-delay: 0.3s; }
.footer-contact { animation-delay: 0.5s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Company section */
.footer-about h3 {
  font-size: 24px;
  margin-bottom: 15px;
  font-weight: 700;
  background: linear-gradient(90deg, #48cae4, #7b2cbf);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
  position: relative;
}

.footer-about h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -5px;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #48cae4, #7b2cbf);
  animation: expandWidth 1s ease-out 0.8s forwards;
}

@keyframes expandWidth {
  to { width: 100%; }
}

.footer-about p {
  font-size: 15px;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

/* Quick links section */
.footer-links h4 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #fff;
  position: relative;
  display: inline-block;
}

.footer-links h4::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -5px;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #48cae4, transparent);
  animation: expandWidth 1s ease-out 1s forwards;
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
  opacity: 0;
  transform: translateX(-20px);
  animation: slideInRight 0.5s ease-out forwards;
}

.footer-links li:nth-child(1) { animation-delay: 0.6s; }
.footer-links li:nth-child(2) { animation-delay: 0.7s; }
.footer-links li:nth-child(3) { animation-delay: 0.8s; }
.footer-links li:nth-child(4) { animation-delay: 0.9s; }

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
  padding: 3px 0;
}

.footer-links a::before {
  content: '›';
  position: absolute;
  left: -15px;
  opacity: 0;
  transition: all 0.3s ease;
}

.footer-links a:hover {
  color: #48cae4;
  transform: translateX(10px);
}

.footer-links a:hover::before {
  opacity: 1;
  left: -10px;
}

/* Contact section */
.footer-contact h4 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #fff;
  position: relative;
  display: inline-block;
}

.footer-contact h4::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -5px;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #48cae4, transparent);
  animation: expandWidth 1s ease-out 1.2s forwards;
}

.footer-contact p {
  font-size: 15px;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
}

.footer-contact p:nth-of-type(1) { animation-delay: 0.7s; }
.footer-contact p:nth-of-type(2) { animation-delay: 0.8s; }

@keyframes fadeIn {
  to { opacity: 1; }
}

.footer-contact p::before {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
}

.footer-contact p:nth-of-type(1)::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%2348cae4' viewBox='0 0 24 24'%3E%3Cpath d='M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z'/%3E%3C/svg%3E");
}

.footer-contact p:nth-of-type(2)::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%2348cae4' viewBox='0 0 24 24'%3E%3Cpath d='M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1zM21 6h-3V3h-2v3h-3v2h3v3h2V8h3z'/%3E%3C/svg%3E");
}

/* Social icons section */
.social-icons {
  display: flex;
  gap: 15px;
  margin-top: 20px;
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.9s forwards;
}

.social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.social-icons a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #48cae4, #7b2cbf);
  opacity: 1;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.social-icons a:hover {
  transform: translateY(-5px) scale(1.1);
}

.social-icons a:hover::before {
  opacity: 2;
}

.social-icons img {
  width: 18px;
  height: 18px;
  transition: all 0.3s ease;
  filter: brightness(0.9);
}

.social-icons a:hover img {
  filter: brightness(1.2);
  transform: scale(1.1);
}

/* Footer bottom */
.footer-bottom {
  text-align: center;
  padding-top: 30px;
  margin-top: 40px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;
  opacity: 0;
  animation: fadeIn 0.5s ease-out 1.2s forwards;
}

.footer-bottom p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.footer-bottom::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #48cae4, transparent);
  animation: expandWidthCentered 1.5s ease-out 1.5s forwards;
}

@keyframes expandWidthCentered {
  to { width: 70%; }
}

/* Wave effect at the top of footer */
.site-footer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 10px;
  background: linear-gradient(90deg, #48cae4, #7b2cbf);
  clip-path: polygon(
    0% 0%, 
    5% 60%, 
    10% 0%, 
    15% 60%, 
    20% 0%, 
    25% 60%, 
    30% 0%, 
    35% 60%, 
    40% 0%, 
    45% 60%, 
    50% 0%, 
    55% 60%, 
    60% 0%, 
    65% 60%, 
    70% 0%, 
    75% 60%, 
    80% 0%, 
    85% 60%, 
    90% 0%, 
    95% 60%, 
    100% 0%
  );
}

/* Hover pulse for the footer */
.site-footer:hover::after {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* Responsive design */
@media (max-width: 992px) {
  .footer-container {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .site-footer {
    padding: 40px 0 20px;
  }
  
  .footer-container {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 15px;
  }
  
  .footer-about {
    text-align: center;
  }
  
  .footer-about h3 {
    font-size: 22px;
  }
  
  .footer-about p {
    font-size: 0.95rem;
    max-width: 100%;
  }
  
  .footer-links {
    text-align: center;
  }
  
  .footer-links h4 {
    font-size: 18px;
  }
  
  .footer-links ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }
  
  .footer-links li {
    margin: 0;
  }
  
  .footer-links a {
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    font-size: 0.95rem;
  }
  
  .footer-links a:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
  
  .footer-contact {
    text-align: center;
  }
  
  .footer-contact h4 {
    font-size: 18px;
  }
  
  .footer-contact p {
    font-size: 0.95rem;
    justify-content: center;
  }
  
  .social-icons {
    justify-content: center;
    gap: 12px;
  }
  
  .social-icons a {
    width: 40px;
    height: 40px;
  }
  
  .social-icons img {
    width: 20px;
    height: 20px;
  }
  
  .footer-bottom {
    margin-top: 30px;
    padding-top: 20px;
  }
  
  .footer-bottom p {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .site-footer {
    padding: 40px 0 15px;
  }
  
  .footer-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .footer-about {
    grid-column: 1;
  }
  
  .footer-bottom::before {
    animation: expandWidthCentered 1.5s ease-out 1.5s forwards;
  }
  
  @keyframes expandWidthCentered {
    to { width: 90%; }
  }
}

/* Tablet-specific adjustments */
@media (min-width: 577px) and (max-width: 768px) {
  .footer-links a:hover {
    transform: translateX(5px);
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .site-footer {
    background: linear-gradient(135deg, #050a14, #0b142a);
  }
  
  .social-icons a {
    background: rgba(6, 249, 241, 0.05);
  }
}

/* Light mode adjustments */
@media (prefers-color-scheme: light) {
  .site-footer {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .site-footer::before,
  .site-footer::after,
  .footer-about,
  .footer-links,
  .footer-contact,
  .footer-links li,
  .footer-contact p,
  .social-icons,
  .footer-bottom {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
  
  .footer-about h3::after,
  .footer-links h4::after,
  .footer-contact h4::after,
  .footer-bottom::before {
    width: 100% !important;
    animation: none !important;
  }
  
  .social-icons a:hover,
  .footer-links a:hover {
    transform: none !important;
  }
}

/* Print styles */
@media print {
  .site-footer {
    background: white !important;
    color: black !important;
    padding: 20px 0 !important;
  }
  
  .site-footer::before,
  .site-footer::after {
    display: none !important;
  }
  
  .footer-about h3,
  .footer-links h4,
  .footer-contact h4 {
    color: black !important;
    -webkit-text-fill-color: initial !important;
  }
  
  .footer-links a,
  .footer-contact p {
    color: #333 !important;
  }
  
  .social-icons {
    display: none !important;
  }
  
  .footer-bottom {
    border-top: 1px solid #ddd !important;
  }
  
  .footer-bottom p {
    color: #333 !important;
  }
}

/* Hero Secondary Section */
.hero-secondary {
    position: relative;
    overflow: hidden;
    background:linear-gradient(90deg, #080f1c, #101d36);
    padding: 0px 0;
}

.hero-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(99, 255, 245, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(232, 131, 255, 0.05) 0%, transparent 50%);
    animation: heroBgPulse 10s ease-in-out infinite alternate;
    z-index: 0;
}

.hero-secondary .hero-image {
    opacity: 0;
    transform: translateX(-50px);
    animation: heroSlideIn 1s ease-out forwards;
    margin-right: 100px;
}

.hero-secondary .hero-image img {
    animation: heroFloat 6s ease-in-out infinite;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
    transition: transform 0.3s ease;
}

.hero-secondary .hero-image img:hover {
    transform: scale(1.02);
}

.hero-secondary .hero-text {
    opacity: 0;
    transform: translateX(50px);
    animation: heroSlideIn 1s ease-out forwards;
}

.hero-secondary .hero-text h2 {
    opacity: 0;
    transform: translateY(-20px);
    animation: heroFadeIn 0.8s ease-out 0.3s forwards;
    color: #0be9dd;
    font-size: 2.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.hero-secondary .hero-text p {
    opacity: 0;
    transform: translateY(20px);
    animation: heroFadeIn 0.8s ease-out 0.5s forwards;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
}

.hero-secondary .features-list {
    opacity: 0;
    animation: heroFadeIn 0.8s ease-out 0.7s forwards;
    list-style: none;
    padding: 0;
    margin: 0 0 30px 0;
}

.hero-secondary .features-list li {
    opacity: 0;
    transform: translateX(-20px);
    animation: heroSlideIn 0.5s ease-out forwards;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.hero-secondary .features-list li:hover {
    transform: translateX(10px);
}

.hero-secondary .features-list li:nth-child(1) { animation-delay: 0.8s; }
.hero-secondary .features-list li:nth-child(2) { animation-delay: 0.9s; }
.hero-secondary .features-list li:nth-child(3) { animation-delay: 1s; }
.hero-secondary .features-list li:nth-child(4) { animation-delay: 1.1s; }

.hero-secondary .features-list li::before {
    content: '✓';
    margin-right: 15px;
    animation: heroBounce 1s ease-in-out infinite;
    font-size: 1.2rem;
}

.hero-secondary .cta-buttons {
    opacity: 0;
    animation: heroFadeIn 0.8s ease-out 1.2s forwards;
}

.hero-secondary .btn-primary {
    animation: heroPulse 2s infinite;
    background: linear-gradient(90deg, #63fff5, #63a4ff);
    color: #0a192f;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.hero-secondary .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(99, 255, 245, 0.2);
}

/* Hero Secondary Animation Keyframes */
@keyframes heroSlideIn {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes heroFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes heroFloat {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes heroBounce {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

@keyframes heroPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 255, 245, 0.4);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(99, 255, 245, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(99, 255, 245, 0);
    }
}

@keyframes heroBgPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.1);
        opacity: 1;
    }
}

/* Hero Secondary Responsive Styles */
@media (max-width: 992px) {
    .hero-secondary {
        padding: 60px 0;
    }

    .hero-secondary .hero-content {
        flex-direction: column;
        text-align: center;
    }

    .hero-secondary .hero-image {
        margin-right: 0;
        margin-bottom: 40px;
    }

    .hero-secondary .hero-text h2 {
        font-size: 2rem;
    }

    .hero-secondary .hero-text p {
        font-size: 1rem;
    }

    .hero-secondary .features-list li {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .hero-secondary {
        padding: 40px 20px;
    }

    .hero-secondary .hero-content {
        gap: 30px;
    }

    .hero-secondary .hero-image {
        max-width: 80%;
        margin: 0 auto 30px auto;
    }

    .hero-secondary .hero-image img {
        width: 100%;
        height: auto;
    }

    .hero-secondary .hero-text {
        padding: 0;
    }

    .hero-secondary .hero-text h2 {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }

    .hero-secondary .features-list li {
        font-size: 1rem;
        margin-bottom: 10px;
    }
}

@media (max-width: 576px) {
    .hero-secondary {
        padding: 30px 15px;
    }

    .hero-secondary .hero-image {
        max-width: 90%;
    }

    .hero-secondary .hero-text h2 {
        font-size: 1.5rem;
        margin-bottom: 12px;
    }

    .hero-secondary .hero-text p {
        font-size: 0.95rem;
        margin-bottom: 20px;
    }

    .hero-secondary .features-list {
        margin-bottom: 20px;
    }

    .hero-secondary .features-list li {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    .hero-secondary .features-list li::before {
        font-size: 1rem;
        margin-right: 10px;
    }

    .hero-secondary .btn-primary {
        padding: 12px 25px;
        font-size: 0.95rem;
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 420px) {
    .hero-secondary {
        padding: 25px 12px;
    }

    .hero-secondary .hero-image {
        max-width: 100%;
        margin-bottom: 20px;
    }

    .hero-secondary .hero-text h2 {
        font-size: 1.4rem;
    }

    .hero-secondary .hero-text p {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .hero-secondary .features-list li {
        font-size: 0.85rem;
        padding: 0 5px;
    }

    .hero-secondary .features-list li::before {
        font-size: 0.9rem;
        margin-right: 8px;
    }

    .hero-secondary .cta-buttons {
        display: flex;
        justify-content: center;
    }

    .hero-secondary .btn-primary {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Hero Secondary Accessibility */
@media (prefers-reduced-motion: reduce) {
    .hero-secondary::before,
    .hero-secondary .hero-image,
    .hero-secondary .hero-image img,
    .hero-secondary .hero-text,
    .hero-secondary .hero-text h2,
    .hero-secondary .hero-text p,
    .hero-secondary .features-list,
    .hero-secondary .features-list li,
    .hero-secondary .features-list li::before,
    .hero-secondary .cta-buttons,
    .hero-secondary .btn-primary {
        animation: none !important;
        opacity: 3 !important;
        transform: none !important;
    }
}

