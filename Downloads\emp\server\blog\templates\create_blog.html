{% extends '.\templates\base.html' %}

{% block title %}
    blog list
{% endblock title %}
{% block content %}



    
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
     

        .container {
            position: relative;
    background: #1e2b3a;
    border-radius: 16px;
    padding: 20px 40px 40px 40px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6);
    width: 100vw;
    margin: auto;
    color: white;
    height: auto;
    margin-top: 20px;
    margin-bottom: 50px;
            
        }

        h1 {
            color: #4e73df;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
        }

        h2 {
            color: #4e73df;
            margin-top: 0;
            font-size: 1.5rem;
        }

        .form-section {
            background-color: #28303b;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 25px;
            border-left: 4px solid #4e73df;
            transition: 0.8s box-shadow;

        }
        .form-section:hover{
            box-shadow: 0 10px 25px rgba(255, 255, 255, 0.6);;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #b0b0b0;
        }

        input[type="text"],
        input[type="number"],
        textarea,
        select {
            width: 100%;
            padding: 10px 15px;
            background-color: #2d2d2d;
            border: 1px solid #444;
            border-radius: 4px;
            color: #e0e0e0;
            font-size: 16px;
            transition: all 0.3s;
        }

        input[type="text"]:focus,
        input[type="number"]:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #4e73df;
            background-color: #3d3d3d;
        }

        .file-input-container {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-input-button {
            width: 100%;
            padding: 10px 15px;
            background-color: #2d2d2d;
            border: 1px solid #444;
            border-radius: 4px;
            color: #aaa;
            font-size: 16px;
            text-align: left;
            cursor: pointer;
        }

        .file-input-button:hover {
            background-color: #3d3d3d;
        }

        .file-input {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        /* Keywords Styles */
        .keywords-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .keyword-tag {
            background-color: #4e73df;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .keyword-tag button {
            background: none;
            border: none;
            color: white;
            margin-left: 8px;
            cursor: pointer;
            padding: 0;
            font-size: 0.8rem;
        }

        /* Subtopic Styles */
        .subtopics-container {
            margin-top: 20px;
        }

        .subtopic-card {
            background-color:#2c3e50;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 3px solid #4e73df;
            position: relative;
        }

        .subtopic-header {
            display: flex;
            margin-bottom: 10px;
        }

        .subtopic-title {
            flex-grow: 1;
            margin-right: 10px;
        }

        .delete-subtopic {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .delete-subtopic:hover {
            background-color: #c82333;
        }

        .add-subtopic {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
        }

        .add-subtopic:hover {
            background-color: #218838;
        }

        /* Submit Button */
        .submit-btn {
            background-color: #4e73df;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1rem;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            margin: 30px auto 0;
            transition: background-color 0.3s;
        }

        .submit-btn:hover {
            background-color: #3a5bbf;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .form-section {
                padding: 15px;
            }
        }
          input.form-control, select.form-control, textarea.form-control {
    background-color: #2c3e50;  /* Dark slate */
    color: #ecf0f1;             /* Light text */
    padding: 10px;
    border: 1px solid #34495e;
    border-radius: 8px;
    margin-bottom: 10px;
    width: 100%;
    font-weight: 600;
    transition: box-shadow 0.3s ease, border-color 0.3s ease;
  }

  input.form-control::placeholder {
    color: #95a5a6; /* Muted gray */
  }

  input.form-control:focus,textarea.form-control:focus, select.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 6px 2px rgba(52, 152, 219, 0.4);  /* Soft blue glow */
    background-color: #34495e;
    color: #ecf0f1;
  }
    .submit {
    background-color: #0ea5e9;
    border: none;
    color: white;
    border-radius: 8px;
    padding: 12px;
    font-weight: 600;
    font-size: 1.4rem;
    transition: background-color 0.3s ease, 0.3s transform ease;
    margin-top: 20px;
    width: 48%;
    cursor: pointer;
  }

  .submit:hover {
    background-color: #2a2f3b;
    border-color: #3498db;
    box-shadow: 0 0 6px 2px rgba(52, 152, 219, 0.4);
    transform:translateY(-8px)
  }

  .cancel {
    background-color: #eb3434e8;
    border: none;
    color: white;
    border-radius: 8px;
    padding: 12px;
    font-weight: 600;
    font-size: 1.4rem;
    transition: background-color 0.3s ease, 0.3s transform ease;
    margin-top: 20px;
    width: 48%;
    cursor: pointer;
  }

  .cancel:hover {
    background-color: #2a2f3b;
    border-color: #3498db;
    box-shadow: 0 0 6px 2px rgba(52, 152, 219, 0.4);
    transform:translateY(-8px)
  }

  .button-row {
    display: flex;
    justify-content: space-between;
    gap: 8px;
  }
   .action-btn {
    width: 20%;
    border-radius: 8px;
    padding: 10px 15px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: white;
  }

  .btn-accept {
    background-color: #10b981;
    border: 1px solid #10b981;
  }

  .btn-decline {
    background-color: #dc2626;
    border: 1px solid #dc2626;
  }

  .action-btn:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-5px);
  }
  .form-header {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
    color: white;
  }
  
  .close-btn {
    position: absolute;
   
    background: transparent;
    border: none;
    font-size: 2rem;
    font-weight: 700;
    color: #ccc;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
  }
  
    </style>
</head>
<body>
    <div class="container">
        <div class="form-header">Add new Blog</div>
          <button type="button" class="close-btn" aria-label="Close form">&times;</button>

       <form method="post" enctype="multipart/form-data" id="blogForm" action="{% url 'create_blog' %}">
            {% csrf_token %}
            <!-- Author Section -->
            <div class="form-section row">
                <h2><i class="fas fa-user-edit"></i> Author Information</h2>
                <div class="form-group col-md-4 mb-3">
                    <label for="author_name" class="form-label">Author Name</label>
                    <input type="text" id="author_name" name="author_name" class="form-control" required>
                </div>
                <div class="form-group col-md-4 mb-3">
                    <label for="author_description">Author Description</label>
                    <input type="text" id="author_description" name="author_description"class="form-control" required>
                </div>
                <div class="form-group col-md-4 mb-3">
                        <label for="author_photo" class="form-label">Author Photo</label>
                        <input type="file" id="author_photo" name="author_photo" class="form-control" accept="image/*" required>
                </div>
            </div>
            
            <!-- Blog Information Section -->
            <div class="form-section row">
                
                <h2><i class="fas fa-book-open"></i> Blog Information</h2>
                <div class="form-group col-md-4 mb-3">
                    <label for="blog_title">Blog Title</label>
                    <input type="text" id="blog_title" name="blog_title" class="form-control" required>
                </div>
                <div class="form-group col-md-4 mb-3">
                    <label for="blog_photo" class="form-label">Blog Cover Photo</label>
                    <input type="file" id="blog_photo" name="blog_photo" class="form-control" accept="image/*" required>
                </div>
                <div class="form-group col-md-4 mb-3">
                    <label for="time_to_read" form="form-label">Estimated Reading Time (minutes) </label>
                    <input type="number" class="form-control" id="time_to_read" name="time_to_read" min="1" required>
                </div>
                <div class="form-group  mb-3">
                    <label for="detail" form="form-label">Blog detail </label>
                    <textarea class="form-control"  name="detail" placeholder="" rows="2" required></textarea>
                </div>
             
                <!-- Keywords Section -->
                <div class="form-group">
                    <label for="keywords_input" class="form-label">Keywords (Press Enter to add)</label>
                    <input type="text" id="keywords_input" class="form-control" placeholder="Type keyword and press Enter">
                    <div id="keywords_container" class="keywords-container"></div>
                    <input type="hidden" id="keywords" name="keywords">
                </div>
            </div>
            
            <!-- Subtopics Section -->
            <div class="form-section row">
                <h2><i class="fas fa-list-ul"></i> Subtopics</h2>
                <div id="subtopics_container" class="subtopics-container">
                    <!-- Subtopics will be added here -->
                </div>
                
                <div class="button-row">
                    <button type="button" id="add_subtopic" class="add-subtopic action-btn btn-accept ">
                      <i class="fas fa-plus"></i> Add Subtopic
                    </button>
                </div>
           
            
            </div>
            
           <div class="button-row">
      <button type="submit" class="submit">Add Blog</button>
      <button type="button" class="cancel" onclick="window.history.back();">Cancel</button>
    </div>
        </form>

      

    </div>
    
    <!-- Subtopic Template (hidden) -->
    <div id="subtopic_template" style="display: none; border: 1px solid rgb(126, 218, 255);">
        <div class="subtopic-card">
            <div class="subtopic-header">
                <input type="text" class="subtopic-title form-control" placeholder="Subtopic Title" f required>
                <button type="button" class="delete-subtopic">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <textarea class="subtopic-description form-control"  placeholder="Subtopic Description" rows="4" required></textarea>
        </div>
    </div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Keywords functionality
    const keywordsInput = document.getElementById('keywords_input');
    const keywordsContainer = document.getElementById('keywords_container');
    const keywordsHidden = document.getElementById('keywords');
    let keywords = [];
    
    keywordsInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const keyword = this.value.trim();
            if (keyword && !keywords.includes(keyword)) {
                keywords.push(keyword);
                updateKeywordsDisplay();
                this.value = '';
            }
        }
    });
    
    function updateKeywordsDisplay() {
        keywordsContainer.innerHTML = '';
        keywords.forEach((keyword, index) => {
            const tag = document.createElement('div');
            tag.className = 'keyword-tag';
            tag.innerHTML = `
                ${keyword}
                <button type="button" data-index="${index}">
                    <i class="fas fa-times"></i>
                </button>
            `;
            keywordsContainer.appendChild(tag);
        });
        keywordsHidden.value = JSON.stringify(keywords);
    }
    
    keywordsContainer.addEventListener('click', function(e) {
        if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
            const button = e.target.tagName === 'BUTTON' ? e.target : e.target.closest('button');
            const index = parseInt(button.getAttribute('data-index'));
            keywords.splice(index, 1);
            updateKeywordsDisplay();
        }
    });
    
    // Subtopics functionality
    const subtopicsContainer = document.getElementById('subtopics_container');
    const subtopicTemplate = document.getElementById('subtopic_template').innerHTML;
    
    document.getElementById('add_subtopic').addEventListener('click', function() {
        const newSubtopic = document.createElement('div');
        newSubtopic.innerHTML = subtopicTemplate;
        subtopicsContainer.appendChild(newSubtopic);
    });
    
    subtopicsContainer.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-subtopic') || e.target.closest('.delete-subtopic')) {
            const button = e.target.classList.contains('delete-subtopic') ? e.target : e.target.closest('.delete-subtopic');
            button.closest('.subtopic-card').remove();
        }
    });

    // Before form submission, update hidden fields with subtopics JSON
    document.getElementById('blogForm').addEventListener('submit', function() {
        const subtopics = [];
        const subtopicCards = document.querySelectorAll('.subtopic-card');
        subtopicCards.forEach(card => {
            subtopics.push({
                title: card.querySelector('.subtopic-title').value,
                description: card.querySelector('.subtopic-description').value
            });
        });
        // Create or update a hidden input for subtopics JSON
        let subtopicsInput = document.getElementById('subtopics_json');
        if (!subtopicsInput) {
            subtopicsInput = document.createElement('input');
            subtopicsInput.type = 'hidden';
            subtopicsInput.name = 'subtopics_json';
            subtopicsInput.id = 'subtopics_json';
            this.appendChild(subtopicsInput);
        }
        subtopicsInput.value = JSON.stringify(subtopics);

        // Also update keywords hidden input (already done in your keywords code)
        keywordsHidden.value = JSON.stringify(keywords);
    });
});
</script>
    
{% endblock content %}