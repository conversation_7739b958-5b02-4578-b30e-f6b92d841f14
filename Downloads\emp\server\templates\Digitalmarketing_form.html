{% extends "header.html" %}
{% block content %}
    <div class="webdev-bg-animation"></div>
    
    <div class="webdev-container-m">
        <div class="webdev-header">
            <h1>Digital Marketing Services</h1>
            <p>Boost your online presence and drive business growth with our comprehensive digital marketing solutions.</p>
        </div>

        <div class="services-grid">
            <!-- SEO Package -->
              {% for ser in service %}
            <div class="service-card-d service-card-seo">
                
                <div class="card-content">
                    <div class="card-left">
                        <div class="card-header">
                                <h3>{{ser.service_name}}</h3>
                            <div class="price">
                                <span class="currency">Rs</span>
                                <span class="amount">{{ser.price}}</span>
                                <span class="period">/month</span>
                            </div>
                        </div>
                        <a href="{% url 'service_form' ser.id %}" class="btn btn-seo">
                            <span class="btn-text apply-btn">Get Started</span>
                            <i class="fas fa-arrow-right btn-icon"></i>
                        </a>
                    </div>
                    <div class="card-right">
                        <h4 class="features-title">What's Included</h4>
                        <ul class="features-list">
                            {% for a in ser.characterstics.all %}
                            <li><i class="fas fa-check"></i><span>{{a.name}}</span></li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
           {% endfor %} 
        </div>
    </div>

    <!-- Contact Form Section -->
    {% comment %} <div class="webdev-container">
        <div class="webdev-contact-section">
            <div class="webdev-contact-header" id="webdev-contact">
                <h2>Request Digital Marketing Services</h2>
                <p>Fill out the form below and we'll get back to you within 24 hours to discuss your digital marketing needs.</p>
            </div>
            <form class="webdev-contact-form" action="#" method="POST">
                <div class="webdev-form-grid">
                    <div class="webdev-form-group">
                        <label for="webdev-dm-name">Full Name</label>
                        <input type="text" id="webdev-dm-name" name="name" required placeholder="Enter your full name">
                    </div>
                    <div class="webdev-form-group">
                        <label for="webdev-dm-email">Email Address</label>
                        <input type="email" id="webdev-dm-email" name="email" required placeholder="Enter your email">
                    </div>
                    <div class="webdev-form-group">
                        <label for="webdev-dm-phone">Phone Number</label>
                        <input type="tel" id="webdev-dm-phone" name="phone" required placeholder="Enter your contact number">
                    </div>
                    <div class="webdev-form-group">
                        <label for="webdev-dm-company">Company Name</label>
                        <input type="text" id="webdev-dm-company" name="company" placeholder="Enter your company name">
                    </div>
                    <div class="webdev-form-group">
                        <label for="webdev-dm-service">Service Required</label>
                        <input type="text" id="webdev-dm-service" name="service" required placeholder="Enter service (e.g., SEO, SMM)">
                    </div>
                    <div class="webdev-form-group">
                        <label for="webdev-dm-budget">Budget Range (in Rs)</label>
                        <input type="number" id="webdev-dm-budget" name="budget" required placeholder="Enter budget">
                    </div>
                    
                </div>
                <div class="webdev-form-group full-width">
                        <label for="webdev-dm-message">Project Details</label>
                        <textarea id="webdev-dm-message" name="message" required placeholder="Please describe your digital marketing goals, target audience, and any specific requirements..."></textarea>
                    </div>
                <div style="display: flex; justify-content: center;">
                    <button type="submit" class="webdev-submit-btn">Submit Request</button>
                </div>
            </form>
        </div>
    </div> {% endcomment %}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create background animation elements
            const bgAnimation = document.querySelector('.webdev-bg-animation');
            
            if (bgAnimation) {
                for (let i = 0; i < 50; i++) {
                    const span = document.createElement('span');
                    const size = Math.random() * 30 + 5;
                    
                    span.style.width = size + 'px';
                    span.style.height = size + 'px';
                    span.style.left = Math.random() * 100 + '%';
                    span.style.top = Math.random() * 100 + '%';
                    span.style.animationDelay = Math.random() * 5 + 's';
                    span.style.animationDuration = Math.random() * 10 + 5 + 's';
                    span.style.opacity = Math.random() * 0.3;
                    span.style.filter = `blur(${Math.random() * 4}px)`;
                    
                    if (i % 4 === 0) {
                        span.style.background = 'rgba(100, 255, 218, 0.1)';
                        span.style.borderRadius = '30% 70% 70% 30% / 30% 30% 70% 70%';
                    } else if (i % 4 === 1) {
                        span.style.background = 'rgba(255, 107, 107, 0.1)';
                        span.style.borderRadius = '50% 50% 20% 80% / 25% 80% 20% 75%';
                    } else if (i % 4 === 2) {
                        span.style.background = 'rgba(77, 171, 247, 0.1)';
                        span.style.borderRadius = '80% 20% 50% 50% / 50% 40% 60% 50%';
                    } else {
                        span.style.background = 'rgba(255, 255, 255, 0.1)';
                        span.style.borderRadius = '60% 40% 30% 70% / 60% 30% 70% 40%';
                    }
                    
                    bgAnimation.appendChild(span);
                }
            }
            
            // Add hover effect to service cards
            const cards = document.querySelectorAll('.service-card-d');
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    cards.forEach(c => {
                        if (c !== card) {
                            c.style.transform = 'scale(0.95)';
                            c.style.opacity = '0.7';
                        }
                    });
                    card.style.transform = 'scale(1.05)';
                    card.style.opacity = '1';
                });
                
                card.addEventListener('mouseleave', () => {
                    cards.forEach(c => {
                        c.style.transform = '';
                        c.style.opacity = '1';
                    });
                });
            });

            // Form field focus effects
            const formGroups = document.querySelectorAll('.webdev-form-group');
            
            formGroups.forEach(group => {
                const input = group.querySelector('input, select, textarea');
                const fieldDescription = group.querySelector('.field-description');

                if (input) {
                    input.addEventListener('focus', () => {
                        group.classList.add('focused');
                        if (fieldDescription) {
                            fieldDescription.style.maxHeight = fieldDescription.scrollHeight + 'px';
                            fieldDescription.style.opacity = '1';
                        }
                    });
                    
                    input.addEventListener('blur', () => {
                        if (!input.value) {
                            group.classList.remove('focused');
                            if (fieldDescription) {
                                fieldDescription.style.maxHeight = '0';
                                fieldDescription.style.opacity = '0';
                            }
                        }
                    });

                    // Initialize for pre-filled values
                    if (input.value) {
                         group.classList.add('focused');
                         if (fieldDescription) {
                            fieldDescription.style.maxHeight = fieldDescription.scrollHeight + 'px';
                            fieldDescription.style.opacity = '1';
                        }
                    } else {
                        if (fieldDescription) {
                            fieldDescription.style.maxHeight = '0';
                            fieldDescription.style.opacity = '0';
                        }
                    }
                }
            });

            // Submit button animation
            const submitBtn = document.querySelector('.webdev-submit-btn');
            
            if (submitBtn) {
                submitBtn.addEventListener('click', function(e) {
                    // Basic ripple effect
                    let ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);

                    let x = e.clientX - e.target.offsetLeft;
                    let y = e.clientY - e.target.offsetTop;

                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);


                    if (!this.form.checkValidity()) {
                        // If form is invalid, don't show loading, allow browser validation to show
                        return;
                    }
                    e.preventDefault(); // Prevent actual submission for demo

                    if (!this.classList.contains('loading')) {
                        this.classList.add('loading');
                        this.innerHTML = '<span class="btn-text">Submitting...</span><div class="spinner"></div>';
                        
                        // Simulate form submission
                        setTimeout(() => {
                            this.classList.remove('loading');
                            this.innerHTML = '<span class="btn-text">Submit Request</span>';
                            // Potentially show a success message or redirect here
                            // this.form.reset(); // Optionally reset form
                            // formGroups.forEach(group => group.classList.remove('focused')); // Reset focus states

                        }, 2500);
                    }
                });
            }

            // Card button hover animations
            const serviceCardButtons = document.querySelectorAll('.service-card-d .btn');
            serviceCardButtons.forEach(btn => {
                btn.addEventListener('mouseenter', () => {
                    const icon = btn.querySelector('.btn-icon');
                    if (icon) icon.style.transform = 'translateX(5px)';
                });
                
                btn.addEventListener('mouseleave', () => {
                    const icon = btn.querySelector('.btn-icon');
                    if (icon) icon.style.transform = 'translateX(0)';
                });
            });
        });
    </script>
   

<head>
    <style>

        body {
            background: linear-gradient(135deg, rgb(66, 41, 92), rgb(25, 45, 78));
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: #ffffff;
        }    

        .webdev-bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
        }

        .webdev-bg-animation span {
            position: absolute;
            display: block;
            animation: float 15s linear infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
            }
        }
        /* Digital Marketing Page Specific Styles */
        .webdev-container-m {
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 20px;
            position: relative;
            z-index: 1;
        }

        .webdev-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .webdev-header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #64ffda, #0b58cb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: fadeInDown 1s ease;
        }

        .webdev-header p {
            font-size: 1.3rem;
            color: #adb3c4;
            max-width: 700px;
            margin: 0 auto;
            animation: fadeInUp 1s ease;
        }

        .services-grid {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin-bottom: 60px;
            perspective: 1000px;
        }

        .service-card-d {
            background: rgba(17, 34, 64, 0.75);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(12px);
            border: 1px solid rgba(100, 255, 218, 0.15);
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            transform-style: preserve-3d;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .service-card-d::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(100deg, transparent, rgba(100, 255, 218, 0.15), transparent);
            transform: skewX(-25deg);
            transition: left 0.7s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        .service-card-d:hover::before {
            left: 100%;
        }

        .service-card-d:hover {
            transform: translateY(-12px) rotateX(3deg) scale(1.03);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            border-color: rgba(100, 255, 218, 0.4);
        }

        .card-content {
            display: flex;
            width: 100%;
            gap: 40px;
            align-items: center;
        }

        .card-left {
            flex: 1;
            padding-right: 40px;
            border-right: 1px solid rgba(100, 255, 218, 0.1);
        }

        .card-right {
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .level-tag {
            position: absolute;
            top: 20px;
            right: -35px;
            padding: 5px 40px;
            transform: rotate(45deg);
            font-size: 0.8rem;
            font-weight: 600;
            background: #64ffda;
            color: #0a192f;
            z-index: 1;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            text-align: left;
            margin-bottom: 0;
        }

        .card-header h3 {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #64ffda;
            position: relative;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .card-header h3::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: #64ffda;
            transition: width 0.3s ease;
        }

        .service-card-d:hover .card-header h3::after {
            width: 100%;
        }

        .price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 15px;
            display: flex;
            align-items: baseline;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .currency {
            font-size: 1.8rem;
            vertical-align: super;
        }

        .period {
            font-size: 1.1rem;
            color: #8892b0;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .features-list li {
            color: #48a8f7;
            margin-bottom: 0;
            opacity: 0.8;
            transform: translateX(-10px);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .features-list li i {
            color: #64ffda;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .service-card-d:hover .features-list li {
            opacity: 1;
            transform: translateX(0);
        }

        .service-card-d:hover .features-list li i {
            transform: scale(1.2);
        }

        .features-list li:nth-child(2n) {
            transform: translateX(10px);
        }

        .btn {
            width: auto;
            padding: 12px 30px;
            margin-top: 20px;
            align-self: flex-start;
            border: none;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        /* Service-specific styles */
        .service-card-seo {
            background: linear-gradient(135deg, rgba(89, 54, 93, 0.95) 0%, rgba(38, 21, 88, 0.95) 100%);
        }

        .service-card-social {
            background:  linear-gradient(135deg, rgba(89, 54, 93, 0.95) 0%, rgba(38, 21, 88, 0.95) 100%);
        }

        .service-card-content {
            background:  linear-gradient(135deg, rgba(89, 54, 93, 0.95) 0%, rgba(38, 21, 88, 0.95) 100%);
        }

        .btn-seo {
            background:rgba(89, 54, 93, 0.95);
            color: #0a192f;
        }

        .btn-social {
            background:rgba(89, 54, 93, 0.95);
            color:rgba(171, 106, 179, 0.95)e;
        }

        .btn-content {
            background:rgba(89, 54, 93, 0.95);
            color: white;
        }

        /* Contact Form Styles */
        .webdev-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 20px;
            position: relative;
            z-index: 1;
        }

        .webdev-contact-section {
            background: rgba(17, 34, 64, 0.75);
            border-radius: 20px;
            padding: 40px;
            margin-top: 60px;
            backdrop-filter: blur(12px);
            border: 1px solid rgba(100, 255, 218, 0.15);
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
        }

        .webdev-contact-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .webdev-contact-header h2 {
            font-size: 2.5rem;
            color: #64ffda;
            margin-bottom: 15px;
        }

        .webdev-contact-header p {
            color: #c9cedf;
            max-width: 600px;
            margin: 0 auto;
        }

        .webdev-contact-form {
            max-width: 800px;
            margin: 0 auto;
        }

        .webdev-form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .webdev-form-group {
            position: relative;
        }

        .webdev-form-group label {
            display: block;
            margin-bottom: 8px;
            color: #64ffda;
            font-size: 0.9rem;
        }

        .webdev-form-group input,
        .webdev-form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(100, 255, 218, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            transition: all 0.3s ease;
        }

        .webdev-form-group input:focus,
        .webdev-form-group textarea:focus {
            outline: none;
            border-color: #64ffda;
            box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.2);
        }

        .webdev-form-group.full-width {
            grid-column: 1 / -1;
        }

        .webdev-submit-btn {
            background: #64ffda;
            color: #0a192f;
            border: none;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto;
            margin-top: 20px;
        }

        .webdev-submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        /* Responsive Form Styles */
        @media (max-width: 992px) {
            .webdev-form-grid {
                grid-template-columns: 1fr;
            }

            .webdev-contact-section {
                padding: 30px 20px;
            }
        }

        @media (max-width: 768px) {
            .webdev-contact-header h2 {
                font-size: 2rem;
            }

            .webdev-contact-section {
                margin-top: 40px;
            }
        }

        @media (max-width: 480px) {
            .webdev-contact-section {
                padding: 20px 15px;
            }

            .webdev-form-group input,
            .webdev-form-group textarea {
                padding: 10px;
            }
        }
    </style>
</head>

 {% include "footer.html" %}
{% endblock %}