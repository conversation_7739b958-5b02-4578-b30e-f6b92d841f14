# Django Framework
Django==5.1.2

# Database (using PyMySQL for better cPanel compatibility)
PyMySQL==1.1.1

# Image Processing (for ImageField and FileField)
Pillow==10.4.0

# Environment Variables (for .env file support)
python-decouple==3.8

# Rich Text Editor
django-ckeditor==6.7.3

# Required for CKEditor
django-js-asset>=2.0

# Core Django dependencies
asgiref>=3.8.1
sqlparse>=0.3.1
tzdata

# Python standard library packages (usually included but ensuring compatibility)
# These are typically built-in but listing for cPanel compatibility
# json - built-in
# datetime - built-in
# decimal - built-in
# pathlib - built-in
# os - built-in

# Additional packages for production hosting
# Uncomment if needed for your hosting environment
# gunicorn==21.2.0
# whitenoise==6.6.0

# Development dependencies (comment out for production)
# django-debug-toolbar==4.4.6
# django-cors-headers==4.4.0