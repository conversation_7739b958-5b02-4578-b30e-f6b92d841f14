{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}IT Company {% endblock %}</title>
    <!-- <link rel="icon" href="{% static 'images/clickdigital.png' %}"  type="image/png"> -->
    <!-- Bootstrap 5 CSS -->
     <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'cssfile/home-w.css' %}">
    <link rel="stylesheet" href="{% static 'cssfile/contact.css' %}">
    <link rel="stylesheet" href="{% static 'cssfile/collab.css' %}">
    <link rel="stylesheet" href="{% static 'cssfile/services.css' %}">
    <link rel="stylesheet" href="{% static 'cssfile/Internship1.css' %}">
    <link rel="stylesheet" href="{% static 'cssfile/abou.css' %}">
    <!-- Add Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    {% comment %} <link rel="stylesheet" href="{% static 'cssfile/forms.css' %}"> {% endcomment %}
    <style>
        .nav-link {
            position: relative;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 50%;
            background-color: #ffc107;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link.active {
            color: #ffc107 !important;
        }
    </style>
</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="/">
            <img src="{% static 'images/clickdigital.png' %}" loading="lazy" alt="Company Logo" style="height: 40px;">
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto me-3">
                <li class="nav-item">
                    <a class="nav-link {% if request.path == '/' %}active{% endif %}" href="{% url 'home' %}">Home</a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link {% if 'services' in request.path %}active{% endif %}" href="{% url 'service_list' %}">Services</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'careers' in request.path %}active{% endif %}" href="{% url 'careers' %}">Careers</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'blogs' in request.path %}active{% endif %}" href="{% url 'blogs' %}">Blogs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'about' in request.path %}active{% endif %}" href="{% url 'about' %}">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'contact' in request.path %}active{% endif %}" href="{% url 'contact' %}">Contact</a>
                </li>
            </ul>
            {% comment %} <form class="d-flex search-form" role="search">
                <input class="form-control search-input" type="search" placeholder="Search..." aria-label="Search">
                <button class="btn" 
                    style="  
        padding: 8px 20px;
        padding-right: 40px;
        margin: 0 10px;
        background-color: #ffc107;
        color: rgb(58, 52, 99);
        font-weight: bold ;
        transition: all 0.3s ease;
        border-radius: 20px
        " type="submit">Search</button>
            </form> {% endcomment %}
        </div>
    </div>
    
</nav>
{% block content %}

{% endblock content %}


</body>
</html>
