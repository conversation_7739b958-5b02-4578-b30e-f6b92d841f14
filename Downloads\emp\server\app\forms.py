# from django import forms

# class ContactForm(forms.Form):
#     name = forms.CharField(
#         max_length=100,
#         required=True,
#         widget=forms.TextInput(attrs={
#             'class': 'form-control',
#             'placeholder': 'Enter your name',
#             'id': 'cd-name'
#         })
#     )
    
#     email = forms.EmailField(
#         required=True,
#         widget=forms.EmailInput(attrs={
#             'class': 'form-control',
#             'placeholder': 'Enter your email',
#             'id': 'cd-email'
#         })
#     )
    
#     phone = forms.CharField(
#         max_length=15,
#         required=False,
#         widget=forms.TextInput(attrs={
#             'class': 'form-control',
#             'placeholder': 'Enter phone number',
#             'id': 'cd-phone'
#         })
#     )
    
#     subject = forms.ChoiceField(
#         choices=[
#             ('', 'Select field'),
#             ('General Inquiry', 'General Inquiry'),
#             ('Web Development', 'Web Development'),
#             ('Digital Marketing', 'Digital Marketing'),
#             ('SEO Services', 'SEO Services'),
#             ('Social Media', 'Social Media Management'),
#             ('Content Creation', 'Content Creation'),
#             ('Partnership', 'Partnership Opportunity'),
#         ],
#         required=True,
#         widget=forms.Select(attrs={
#             'class': 'form-control',
#             'id': 'cd-subject'
#         })
#     )
    
#     message = forms.CharField(
#         required=True,
#         widget=forms.Textarea(attrs={
#             'class': 'form-control',
#             'placeholder': 'How can we help?',
#             'rows': 5,
#             'id': 'cd-message'
#         })
#     ) 