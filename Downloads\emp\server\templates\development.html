{% extends "header.html" %}
{% block content %}
     <style>
        body {
            background: linear-gradient(135deg, rgb(66, 41, 92), rgb(25, 45, 78));
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: #ffffff;
        }    

        .webdev-bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
        }

        .webdev-bg-animation span {
            position: absolute;
            display: block;
            animation: float 15s linear infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
            }
        }

        /* Development Page Specific Styles */
        .webdev-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 20px;
            position: relative;
            z-index: 1;
        }

        .webdev-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .webdev-header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #64ffda, #0b58cb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: fadeInDown 1s ease;
        }

        .webdev-header p {
            font-size: 1.3rem;
            color: #adb3c4;
            max-width: 700px;
            margin: 0 auto;
            animation: fadeInUp 1s ease;
        }

        .webdev-pricing-grid {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin-bottom: 60px;
            perspective: 1000px;
        }

        .webdev-pricing-card {
            background: rgba(17, 34, 64, 0.75);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(12px);
            border: 1px solid rgba(100, 255, 218, 0.15);
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            transform-style: preserve-3d;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .webdev-pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(100deg, transparent, rgba(100, 255, 218, 0.15), transparent);
            transform: skewX(-25deg);
            transition: left 0.7s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        .webdev-pricing-card:hover::before {
            left: 100%;
        }

        .webdev-pricing-card:hover {
            transform: translateY(-12px) rotateX(3deg) scale(1.03);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            border-color: rgba(100, 255, 218, 0.4);
        }

        .webdev-card-content {
            display: flex;
            width: 100%;
            gap: 40px;
            align-items: center;
        }

        .webdev-card-left {
            flex: 1;
            padding-right: 40px;
            border-right: 1px solid rgba(100, 255, 218, 0.1);
        }

        .webdev-card-right {
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .webdev-level-tag {
            position: absolute;
            top: 20px;
            right: -35px;
            padding: 5px 40px;
            transform: rotate(45deg);
            font-size: 0.8rem;
            font-weight: 600;
            background: #64ffda;
            color: #0a192f;
            z-index: 1;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .webdev-card-header {
            text-align: left;
            margin-bottom: 0;
        }

        .webdev-card-header h3 {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #64ffda;
            position: relative;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .webdev-card-header h3::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: #64ffda;
            transition: width 0.3s ease;
        }

        .webdev-pricing-card:hover .webdev-card-header h3::after {
            width: 100%;
        }

        .webdev-price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 15px;
            display: flex;
            align-items: baseline;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .webdev-currency {
            font-size: 1.8rem;
            vertical-align: super;
        }

        .webdev-period {
            font-size: 1.1rem;
            color: #8892b0;
        }

        .webdev-features-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .webdev-features-list li {
            color: #48a8f7;
            margin-bottom: 0;
            opacity: 0.8;
            transform: translateX(-10px);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .webdev-features-list li i {
            color: #64ffda;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .webdev-pricing-card:hover .webdev-features-list li {
            opacity: 1;
            transform: translateX(0);
        }

        .webdev-pricing-card:hover .webdev-features-list li i {
            transform: scale(1.2);
        }

        .webdev-features-list li:nth-child(2n) {
            transform: translateX(10px);
        }

        .webdev-btn {
            width: auto;
            padding: 12px 30px;
            margin-top: 20px;
            align-self: flex-start;
            border: none;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .webdev-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .webdev-btn-standard {
            background: #64ffda;
            color: #0a192f;
        }

        .webdev-btn-premium {
            background: #ff6b6b;
            color: white;
        }

        .webdev-btn-enterprise {
            background: #4dabf7;
            color: white;
        }

        /* Contact Form Styles */
        .webdev-contact-section {
            background: rgba(17, 34, 64, 0.75);
            border-radius: 20px;
            padding: 40px;
            margin-top: 60px;
            backdrop-filter: blur(12px);
            border: 1px solid rgba(100, 255, 218, 0.15);
        }

        .webdev-contact-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .webdev-contact-header h1 {
            font-size: 2.5rem;
            color: #64ffda;
            margin-bottom: 15px;
        }

        .webdev-contact-header p {
            color: #8892b0;
            max-width: 600px;
            margin: 0 auto;
        }

        .webdev-form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .webdev-form-group {
            position: relative;
        }

        .webdev-form-group label {
            display: block;
            margin-bottom: 8px;
            color: #64ffda;
            font-size: 0.9rem;
        }

        .webdev-form-group input,
        .webdev-form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(100, 255, 218, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            transition: all 0.3s ease;
        }

        .webdev-form-group input:focus,
        .webdev-form-group textarea:focus {
            outline: none;
            border-color: #64ffda;
            box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.2);
        }

        .webdev-form-group.full-width {
            grid-column: 1 / -1;
        }

        .webdev-submit-btn {
            background: #64ffda;
            color: #0a192f;
            border: none;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .webdev-submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Styles */
        @media (max-width: 1200px) {
            .webdev-container {
                padding: 60px 20px;
            }

            .webdev-header h1 {
                font-size: 3rem;
            }
        }

        @media (max-width: 992px) {
            .webdev-card-content {
                flex-direction: column;
                gap: 30px;
            }

            .webdev-card-left {
                padding-right: 0;
                border-right: none;
                border-bottom: 1px solid rgba(100, 255, 218, 0.1);
                padding-bottom: 30px;
                width: 100%;
            }

            .webdev-features-list {
                grid-template-columns: 1fr;
            }

            .webdev-form-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .webdev-header h1 {
                font-size: 2.5rem;
            }

            .webdev-header p {
                font-size: 1.1rem;
            }

            .webdev-pricing-card {
                padding: 25px;
            }

            .webdev-card-header h3 {
                font-size: 1.8rem;
            }

            .webdev-price {
                font-size: 2rem;
            }

            .webdev-contact-section {
                padding: 30px 20px;
            }

            .webdev-contact-header h1 {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .webdev-header h1 {
                font-size: 2rem;
            }

            .webdev-pricing-card {
                padding: 20px;
            }

            .webdev-card-header h3 {
                font-size: 1.5rem;
            }

            .webdev-price {
                font-size: 1.8rem;
            }

            .webdev-btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style> 
   

  <div class="webdev-bg-animation"></div>
    
            <div class="webdev-container">
                <div class="webdev-header">
                    <h1>Our Web Development Services</h1>
                    <p>Transform your digital presence with our cutting-edge web development solutions. We create stunning, high-performance websites that drive business growth.</p>
                </div>

        <div class="webdev-pricing-grid">
            <!-- Standard Package -->
            {% for i in queryset %}
            <div class="webdev-pricing-card standard">
                <div class="webdev-card-content">
                    <div class="webdev-card-left">
                        <div class="webdev-card-header">
                            <h3>{{ i.service_name }}</h3>
                            <div class="webdev-price">
                                <span class="webdev-currency">Rs</span>
                                <span class="webdev-amount">{{ i.price }}</span>
                                <span class="webdev-period">/project</span>
                            </div>
                        </div>
                        <a href="{% url 'service_form' i.id %}" class="webdev-btn webdev-btn-standard">Get Started</a>
                    </div>
                    <div class="webdev-card-right">
                        <h4 class="features-title">What's Included</h4>
                        <ul class="webdev-features-list">
                            {% for a in i.characterstics.all %}
                            <li><i class="fas fa-check"></i> {{ a.name }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                
            </div>
            {% endfor %}
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create background animation elements
            const bgAnimation = document.querySelector('.webdev-bg-animation');
            
            for (let i = 0; i < 50; i++) {
                const span = document.createElement('span');
                const size = Math.random() * 30 + 5;
                
                span.style.width = size + 'px';
                span.style.height = size + 'px';
                span.style.left = Math.random() * 100 + '%';
                span.style.top = Math.random() * 100 + '%';
                span.style.animationDelay = Math.random() * 5 + 's';
                span.style.animationDuration = Math.random() * 10 + 5 + 's';
                span.style.opacity = Math.random() * 0.3;
                span.style.filter = `blur(${Math.random() * 4}px)`;
                
                if (i % 4 === 0) {
                    span.style.background = 'rgba(100, 255, 218, 0.1)';
                    span.style.borderRadius = '30% 70% 70% 30% / 30% 30% 70% 70%';
                } else if (i % 4 === 1) {
                    span.style.background = 'rgba(255, 107, 107, 0.1)';
                    span.style.borderRadius = '50% 50% 20% 80% / 25% 80% 20% 75%';
                } else if (i % 4 === 2) {
                    span.style.background = 'rgba(77, 171, 247, 0.1)';
                    span.style.borderRadius = '80% 20% 50% 50% / 50% 40% 60% 50%';
                } else {
                    span.style.background = 'rgba(255, 255, 255, 0.1)';
                    span.style.borderRadius = '60% 40% 30% 70% / 60% 30% 70% 40%';
                }
                
                bgAnimation.appendChild(span);
            }
            
            // Add hover effect to pricing cards
            const cards = document.querySelectorAll('.webdev-pricing-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    cards.forEach(c => {
                        if (c !== card) {
                            c.style.transform = 'scale(0.95)';
                            c.style.opacity = '0.7';
                        }
                    });
                    card.style.transform = 'scale(1.05)';
                    card.style.opacity = '1';
                });
                
                card.addEventListener('mouseleave', () => {
                    cards.forEach(c => {
                        c.style.transform = '';
                        c.style.opacity = '1';
                    });
                });
            });

            // Form field focus effects
            const formGroups = document.querySelectorAll('.webdev-form-group');
            
            formGroups.forEach(group => {
                const input = group.querySelector('input, select, textarea');
                
                input.addEventListener('focus', () => {
                    group.classList.add('focused');
                });
                
                input.addEventListener('blur', () => {
                    if (!input.value) {
                        group.classList.remove('focused');
                    }
                });
            });

            // Submit button animation
            const submitBtn = document.querySelector('.webdev-submit-btn');
            
            submitBtn.addEventListener('click', function(e) {
                if (!this.classList.contains('loading')) {
                    this.classList.add('loading');
                    
                    // Simulate form submission (remove this in production)
                    setTimeout(() => {
                        this.classList.remove('loading');
                    }, 2000);
                }
            });
        });
    </script>

 {% include "footer.html" %}
 {% endblock %}