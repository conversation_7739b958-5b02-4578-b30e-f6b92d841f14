from django.db import models

# Create your models here.
class Collab(models.Model):
    company_name=models.CharField(max_length=50)
    email=models.EmailField(max_length=50)
    industry=models.CharField(max_length=50)
    description=models.TextField(max_length=200)
    contact=models.IntegerField(max_length=15)

    def __str__(self) -> str:
        return self.company_name

class Partners(models.Model):
    partner=models.ForeignKey(Collab,on_delete=models.CASCADE,related_name='partner')

class ContactUs(models.Model):
    name=models.CharField(max_length=50)
    email=models.EmailField(max_length=50)
    contact=models.IntegerField(max_length=100)
    subject=models.Char<PERSON>ield(max_length=50)
    message=models.TextField(max_length=100)

    def __str__(self) -> str:
        return self.name
    

