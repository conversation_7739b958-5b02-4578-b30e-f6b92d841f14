from django.urls import path
from .views import *
from vacancy.views import *
from projects.views import *
from collab.views import *
from blog.views import *

urlpatterns = [
    path('',home,name='home'),
    path('blogs/', blogs, name='blogs'),
    path('blogs/<int:id>/',read_blog, name='read_blog'),
    path('careers/', careers, name='careers'),
    path('contact/',create_contact,name='contact'),
    path('services/',service_list_frontt,name='service_list'),
    path('applicant/<int:id>/',application_form, name='applicant'),
    path('about/',about,name='about'),
    path('collab/',create_collab,name='collab'),
    path('development/',development_services,name='development'),
    path('developent/<int:id>/',service_form,name='service_form'),
    path('Digitalmarketing_form/',marketing_services,name='Digitalmarketing_form'),
    # path('login/',login,name='login'),
]