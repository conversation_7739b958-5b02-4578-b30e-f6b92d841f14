{% extends 'base.html' %}
{% block title %}
Filter Applicant
{% endblock title %}
{% block content %}
<style>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: rgba(31, 44, 59, 0.95);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 30px;
    width: 90%;
    max-width: 500px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .modal-header {
    margin-bottom: 20px;
    text-align: center;
  }

  .modal-title {
    color: #38bdf8;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .project-info {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 12px;
    margin-bottom: 20px;
  }

  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .info-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .info-label {
    color: #94a3b8;
    font-size: 0.9rem;
  }

  .info-value {
    color: #ffffff;
    font-weight: 500;
  }

  .rating-section {
    margin-bottom: 25px;
  }

  .rating-label {
    color: #38bdf8;
    font-size: 1.1rem;
    margin-bottom: 15px;
    display: block;
  }

  .rating-stars {
    display: flex;
    gap: 10px;
    justify-content: center;
  }

  .star {
    font-size: 2rem;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .star:hover,
  .star.active {
    color: #fbbf24;
    transform: scale(1.1);
  }

  .button-row {
    display: flex;
    gap: 15px;
  }

  .action-btn {
    flex: 1;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: white;
  }

  .btn-submit {
    background-color: #10b981;
    border: 1px solid #10b981;
  }

  .btn-cancel {
    background-color: #ef4444;
    border: 1px solid #ef4444;
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
</style>

<div class="modal-overlay">
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title">Filter Appplicant</h2>
    </div>

    <div class="project-info">
      <div class="info-row">
        <span class="info-label">Name</span>
        <span class="info-value">{{applicant.name}}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Title</span>
        <span class="info-value">{{applicant.vacancydetails.position}}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Faculty</span>
        <span class="info-value">{{applicant.vacancydetails.faculty}}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Experience</span>
        <span class="info-value">{{applicant.experience}} years</span>
      </div>
      <div class="info-row">
        <span class="info-label">Education</span>
        <span class="info-value">{{applicant.education}} </span>
      </div>
    </div>

    <form method="POST" action="{% url 'filter' applicant.id %}">
      {% csrf_token %}
      <div class="rating-section">
        <label class="rating-label">Rate Applicant</label>
        <div class="rating-stars">
          {% for i in rate %}
          <span class="star" data-rating="{{ i }}">
            <i class="fas fa-star"></i>
          </span>
          {% endfor %}
        </div>
        <input type="hidden" name="rating" id="rating-input" required>
      </div>

      <div class="button-row">
        <button type="submit" class="action-btn btn-submit">
          <i class="fas fa-check"></i>
          Filter
        </button>
        <a href="{% url 'applicant_details' applicant.id %}" class="action-btn btn-cancel">
          <i class="fas fa-times"></i>
          Cancel
        </a>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const stars = document.querySelectorAll('.star');
    const ratingInput = document.getElementById('rating-input');

    stars.forEach(star => {
      star.addEventListener('click', function() {
        const rating = this.dataset.rating;
        ratingInput.value = rating;
        
        // Update stars appearance
        stars.forEach(s => {
          if (s.dataset.rating <= rating) {
            s.classList.add('active');
          } else {
            s.classList.remove('active');
          }
        });
      });
    });
  });
</script>
{% endblock %} 