<div class="mb-3">
    <label class="form-label">Select Employees</label>
    <div class="available-employees mb-3">
        <h6 class="mb-2">Available Employees:</h6>
        <div class="d-flex flex-wrap gap-2">
            {% for user in available_users %}
            <div class="employee-card p-2 mb-2" style="cursor: pointer; background-color: #2c3e50; border: 1px solid #34495e; border-radius: 8px;" 
                 onclick="addEmployee('{{ user.name }}', '{{ user.faculty }}', '{{ user.image.url }}')">
                <div class="d-flex align-items-center gap-2">
                    <img src="{{ user.image.url }} " 
                         alt="{{ user.name }}" 
                         style="width: 40px; height: 40px; border-radius: 50%;" loading="lazy">
                    <div>
                        <h6 class="mb-0 text-white">{{ user.name }}</h6>
                        <small class="text-muted d-block">{{ user.faculty }}</small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    <div id="selectedEmployees" class="mt-3">
        <h6 class="mb-2">Selected Employees:</h6>
        <div id="selectedEmployeesList" class="d-flex flex-wrap gap-2"></div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectedEmployeesList = document.getElementById('selectedEmployeesList');
    let selectedEmployees = new Set();

    window.addEmployee = function(name, faculty, image) {
        if (!selectedEmployees.has(name)) {
            selectedEmployees.add(name);
            const employeeTag = document.createElement('div');
            employeeTag.className = 'selected-employee-tag';
            employeeTag.innerHTML = `
                <div class="d-flex align-items-center gap-2 bg-light p-2 rounded">
                    <img src="${image}" 
                         alt="${name}" 
                         style="width: 30px; height: 30px; border-radius: 50%;">
                    <div>
                        <span>${name}</span>
                        <small class="d-block text-muted">${faculty}</small>
                    </div>
                    <button type="button" class="btn-close btn-close-white" 
                            onclick="removeEmployee('${name}', this)"></button>
                    <input type="hidden" name="assigned_users" value="${name}">
                </div>
            `;
            selectedEmployeesList.appendChild(employeeTag);
        }
    };

    window.removeEmployee = function(userName, button) {
        selectedEmployees.delete(userName);
        button.closest('.selected-employee-tag').remove();
    };
});
</script>
