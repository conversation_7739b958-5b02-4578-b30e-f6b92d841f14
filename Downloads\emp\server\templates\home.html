{% extends "header.html" %}
{% load static %}

{% block content %}

<style>


    .team-showcase {
        padding: 100px 0;
        background: linear-gradient(135deg, #0606da 0%,rgb(21, 64, 184) 100%);
        position: relative;
        overflow: hidden;
    }
    
    .team-showcase::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
            radial-gradient(circle at 20% 20%, rgba(8, 65, 61, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(58, 15, 68, 0.05) 0%, transparent 50%);
        animation: teamBgPulse 15s ease-in-out infinite alternate;
        z-index: 0;
    }
    
    @keyframes teamBgPulse {
        0% { transform: scale(1) rotate(0deg); }
        50% { transform: scale(1.05) rotate(1.5deg); }
        100% { transform: scale(1.1) rotate(3deg); }
    }
    .showcase-title{
        font-size: 2.2rem;
        background: linear-gradient(45deg,rgb(159, 130, 206),rgb(249, 174, 89));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent; 
        text-align: center;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
        position: relative;
        z-index: 1;
    }

    .showcase-title::after{
        content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 4px;
    background: linear-gradient(135deg, #11ff00 0%, #fd042a 100%);
    border-radius: 2px;
    animation: titleUnderline 1.5s ease-out 0.5s both;
    }

    .showcase-subtitle{
        margin-top: 20px;
        font-size: 1rem;
        color: #fff;
        text-align: center;
        margin-bottom: 30px;
        font-weight: 500;
    }
    
    .team-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        margin-top: 50px;
        position: relative;
        z-index: 1;
        perspective: 1000px;
    }
    
    .team-card {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        opacity: 0;
        transform: translateY(30px) rotateX(10deg);
        animation: teamCardFadeIn 0.6s ease forwards;
        transform-style: preserve-3d;
    }
    
    .team-card:hover {
        transform: translateY(-10px) rotateX(0deg);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2),
                    0 0 20px rgba(99, 255, 245, 0.1),
                    0 0 40px rgba(232, 131, 255, 0.1);
        background: rgba(255, 255, 255, 0.1);
    }
    
    .card-image {
        position: relative;
        width: 200px;
        height: 200px;
        margin: auto auto;
        margin-top: 15px;
        border-radius: 50%;
        overflow: hidden;
        border: 4px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        transition: all 0.4s ease;
        animation: imageFloat 6s ease-in-out infinite;
    }
    
    
    .card-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(99, 255, 245, 0.2), rgba(232, 131, 255, 0.2));
        opacity: 0;
        transition: opacity 0.4s ease;
        z-index: 1;
        border-radius: 50%;
    }
    
    .team-card:hover .card-image {
        transform: scale(1.1);
        border-color: rgba(99, 255, 245, 0.5);
        box-shadow: 0 0 30px rgba(99, 255, 245, 0.3);
    }
    
    .team-card:hover .card-image::before {
        opacity: 1;
        animation: gradientPulse 2s ease-in-out infinite;
    }
    
    .card-image img {
        width: 100%;
        height:auto;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .team-card:hover .card-image img {
        transform: scale(1.1);
    }
    
    .card-content {
        padding: 25px;
        text-align: center;
        position: relative;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        transform: translateZ(10px);
        transition: transform 0.3s ease;
        margin-top: 20px;
    }
    
    .team-card:hover .card-content {
        transform: translateZ(30px);
    }
    
    .card-content h3 {
        font-size: 1.5rem;
        color: #fff;
        margin-bottom: 10px;
        font-weight: 600;
        position: relative;
        display: inline-block;
        transition: transform 0.3s ease;
    }
    
    .team-card:hover .card-content h3 {
        transform: scale(1.05);
    }
    
    .card-content h3::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, #63fff5, #e883ff);
        transition: width 0.4s ease;
    }
    
    .team-card:hover .card-content h3::after {
        width: 50%;
        animation: borderPulse 2s ease-in-out infinite;
    }
    
    .card-content .role {
        color: #63fff5;
        font-size: 1rem;
        margin-bottom: 15px;
        font-weight: 500;
    }
    
    .card-content .bio {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 20px;
    }
    
    .social-media {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 20px;
        transform: translateZ(20px);
    }
    
    .media-link {
        color: rgba(25, 236, 74, 0.7);
        font-size: 1.2rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
        transform-style: preserve-3d;
    }
    
    .media-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #63fff5, #e883ff);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
    }
    
    .media-link:hover {
        color: #fff;
        transform: translateY(-3px) translateZ(30px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        background: linear-gradient(135deg, #63fff5, #e883ff);
    }
    
    .media-link:hover::before {
        opacity: 1;
        animation: iconGlow 1.5s ease-in-out infinite;
    }
    
    /* Animation Keyframes */
    @keyframes teamCardFadeIn {
        from {
            opacity: 0;
            transform: translateY(30px) rotateX(10deg);
        }
        to {
            opacity: 1;
            transform: translateY(0) rotateX(0deg);
        }
    }
    
    /* Staggered Animation for Team Cards */
    .team-card:nth-child(1) { animation-delay: 0.2s; }
    .team-card:nth-child(2) { animation-delay: 0.4s; }
    .team-card:nth-child(3) { animation-delay: 0.6s; }
    
    /* Responsive Styles */
    @media (max-width: 1024px) {
        .team-showcase {
            padding: 80px 0;
        }
    
        .team-cards {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }
    
        .card-image {
            height: 280px;
        }
    }
    
    @media (max-width: 768px) {
        .team-showcase {
            padding: 60px 0;
        }
    
        .team-cards {
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
        }
    
        .card-image {
            width: 150px;
            height: 150px;
        }
    
        .card-content {
            padding: 20px;
        }
    
        .card-content h3 {
            font-size: 1.3rem;
        }
    
        .card-content .role {
            font-size: 0.9rem;
        }
    
        .card-content .bio {
            font-size: 0.9rem;
        }
    
        .media-link {
            width: 35px;
            height: 35px;
            font-size: 1.1rem;
        }
    }
    
    @media (max-width: 480px) {
        .team-showcase {
            padding: 50px 0;
        }
    
        .team-cards {
            grid-template-columns: 1fr;
            max-width: 320px;
            margin-left: auto;
            margin-right: auto;
        }
    
        .card-image {
            width: 120px;
            height: 120px;
        }
    
        .card-content {
            padding: 15px;
        }
    
        .card-content h3 {
            font-size: 1.2rem;
        }
    
        .card-content .role {
            font-size: 0.85rem;
        }
    
        .card-content .bio {
            font-size: 0.85rem;
            margin-bottom: 15px;
        }
    
        .social-media {
            gap: 12px;
        }
    
        .media-link {
            width: 32px;
            height: 32px;
            font-size: 1rem;
        }
    }
    
    /* Dark Mode Support */
    @media (prefers-color-scheme: dark) {
        .team-showcase {
            background: linear-gradient(135deg, #0a0a1a 0%, #0d1b2a 100%);
        }
    
        .team-card {
            background: rgba(255, 255, 255, 0.03);
        }
    
        .card-content {
            background: rgba(255, 255, 255, 0.03);
        }
    }
    
    /* Reduced Motion */
    @media (prefers-reduced-motion: reduce) {
        .team-showcase::before,
        .team-card,
        .card-image img,
        .media-link,
        .card-content h3,
        .card-content h3::after,
        .card-image::before {
            animation: none !important;
            transition: none !important;
        }
    
        .team-card {
            opacity: 1;
            transform: none;
        }
    
        .team-card:hover {
            transform: none;
        }
    
        .card-content h3::after {
            width: 50%;
        }
    } 
    
    /* Requirements List Styling */
.requirements-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .requirements-list li {
    position: relative;
    padding: 12px 20px;
    padding-left: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(99, 255, 245, 0.1);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    transition: all 0.3s ease;
    animation: slideInRight 0.5s ease-out forwards;
    opacity: 0;
  }
  
  .requirements-list li:nth-child(1) { animation-delay: 0.1s; }
  .requirements-list li:nth-child(2) { animation-delay: 0.2s; }
  .requirements-list li:nth-child(3) { animation-delay: 0.3s; }
  .requirements-list li:nth-child(4) { animation-delay: 0.4s; }
  
  .requirements-list li::before {
    content: '✓';
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #63fff5;
    font-weight: bold;
    font-size: 1.1rem;
    opacity: 0.8;
    transition: all 0.3s ease;
  }
  
  .requirements-list li:hover {
    transform: translateX(10px);
    background: rgba(99, 255, 245, 0.1);
    border-color: rgba(99, 255, 245, 0.2);
    box-shadow: 0 5px 15px rgba(99, 255, 245, 0.1);
  }
  
  .requirements-list li:hover::before {
    color: #63fff5;
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
  
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  /* Responsive adjustments for requirements list */
  @media (max-width: 768px) {
    .requirements-list {
      gap: 10px;
    }
    
    .requirements-list li {
      padding: 10px 15px;
      padding-left: 35px;
      font-size: 0.95rem;
    }
    
    .requirements-list li::before {
      left: 12px;
      font-size: 1rem;
    }
  }
  
  @media (max-width: 480px) {
    .requirements-list li {
      padding: 8px 12px;
      padding-left: 30px;
      font-size: 0.9rem;
    }
    
    .requirements-list li::before {
      left: 10px;
      font-size: 0.9rem;
    }
  }
  
  /* Dark mode adjustments */
  @media (prefers-color-scheme: dark) {
    .requirements-list li {
      background: rgba(255, 255, 255, 0.03);
      border-color: rgba(99, 255, 245, 0.08);
    }
    
    .requirements-list li:hover {
      background: rgba(99, 255, 245, 0.08);
    }
  }
  
  /* Print styles */
  @media print {
    .requirements-list li {
      background: none;
      border: 1px solid #ddd;
      color: #333;
      break-inside: avoid;
    }
    
    .requirements-list li::before {
      color: #333;
    }
  }



</style>

<section class="hero">
    <div class="hero-content">
        <div class="hero-text">
            <h1>Start your Digital Journey with 
                <span class="animated-text">
                  <span>C</span><span>l</span><span>i</span><span>c</span><span>k</span><span>_</span><span>D</span><span>i</span><span>g</span><span>i</span><span>t</span><span>a</span><span>l</span><span>s</span><span>!</span>
              </h1>
            <p>Empower your business with Click_Digitals' innovative IT solutions. <br>
We specialize in custom software development, digital marketing, and transformation services for your growth.</p>
            <div class="cta-buttons">
                <a href="{% url 'service_list' %}" class="btn btn-primary">Get Started</a>
                <a href="{% url 'about' %}" class="btn btn-secondary">Learn More</a>
            </div>
        </div>
        <div class="hero-image">
            <img src="{% static 'images/click.png.jpg' %}" loading="lazy"  alt="Technology Illustration">
        </div>
    </div>
</section>

<section class="hero hero-secondary">
    <div class="hero-content">
        <div class="hero-image">
            <img src="{% static 'images/click.png.jpg' %}" loading="lazy" alt="Team working together">
        </div>
        <div class="hero-text">
            <h2>Why Choose Click Digitals?</h2>
            <p>We're more than a tech agency — we're your growth partner. From concept to launch, we deliver custom digital solutions designed to accelerate your business success.</p>
            <ul class="features-list">
                <li>Tailored IT Solutions to Drive Business Success</li>
                <li>Agile Software Development for Rapid Results</li>
                <li>Performance-Focused Digital Marketing Strategies</li>
                <li>Expert IT Support & Proactive Consulting</li>
            </ul>
            <div class="cta-buttons">
                <a href="{% url 'collab'%}" class="btn btn-primary">Schedule meeting</a>
            </div>
        </div>
    </div>
</section>

<section class="services-section">
    <div class="container">
        <h2 class="section-title">Our Core Services</h2>
        <p class="section-subtitle">Unlock Your Business Potential with Our Expert IT & Digital Marketing Services</p>
        <div class="services-grid">
            
            <div class="service-card">
                <img src="static/images/webdev.webp" alt="Software Development">
                <h3>Web Development</h3>
                
                <ul class="requirements-list">
                    <li>Dynamic, SEO-Optimized Websites for Top Visibility</li> 
                    <li>Mobile-Responsive Designs for better Experience</li>
                    <li>Secure and Scalable Web Solutions Tailored to Your Business</li>
               </ul>
                <a href="{% url 'development' %}" class="apply-btn" target="_blank">Apply Now</a>
            </div>
            <div class="service-card">
                <img src="static/images/digital.webp" loading="lazy" alt="Software Development">
                <h3>Digital Marketing</h3>
                
                <ul class="requirements-list">
                    <li>Result-Driven SEO Strategies for Organic Growth</li> 
                    <li>Comprehensive Digital Marketing Campaigns</li>
                    <li>Expert SEO Services to Boost Organic Reach and Rankings</li>
               </ul>
                <a href="{% url 'Digitalmarketing_form' %}" loading="lazy" class="apply-btn" target="_blank">Apply Now</a>
            </div>
        </div>
    </div>
</section>

<section class="cd-success-stories-section">
    <div class="cd-success-container">
        <h2 class="cd-section-title">Client Success Stories</h2>
        <p class="cd-section-subtitle">Proven Success That Powers Business Growth</p>
        
        <div class="cd-success-slider">
            <div class="cd-success-card active">
                <div class="cd-success-content">
                    <div class="cd-company-info">
                        <div class="cd-company-logo">techstart</div>
                        <div class="cd-company-details">
                            <h4>TechStart Inc.</h4>
                            <span>B2B SaaS Platform</span>
                        </div>
                    </div>
                    <blockquote>
                        "The team delivered a robust platform that scaled from 100 to 10,000 users in just 6 months. 
                        Their expertise in cloud architecture was invaluable."
                    </blockquote>
                    <div class="cd-success-metrics">
                        <div class="cd-metric">
                            <span class="cd-metric-value">300%</span>
                            <span class="cd-metric-label">User Growth</span>
                        </div>
                        <div class="cd-metric">
                            <span class="cd-metric-value">$2M</span>
                            <span class="cd-metric-label">Revenue Generated</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="cd-success-card">
                <div class="cd-success-content">
                    <div class="cd-company-info">
                        <div class="cd-company-logo">FinCorp</div>
                        <div class="cd-company-details">
                            <h4>FinCorp Solutions</h4>
                            <span>Financial Technology</span>
                        </div>
                    </div>
                    <blockquote>
                        "They built a secure, compliant trading platform that handles millions of transactions daily. 
                        The performance and reliability exceeded our expectations."
                    </blockquote>
                    <div class="cd-success-metrics">
                        <div class="cd-metric">
                            <span class="cd-metric-value">500K</span>
                            <span class="cd-metric-label">Daily Transactions</span>
                        </div>
                        <div class="cd-metric">
                            <span class="cd-metric-value">99.99%</span>
                            <span class="cd-metric-label">Uptime Achieved</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="cd-slider-controls">
            <button class="cd-slider-btn prev" onclick="changeStory(-1)">❮</button>
            <div class="cd-slider-dots">
                <span class="cd-dot active" onclick="currentStory(1)"></span>
                <span class="cd-dot" onclick="currentStory(2)"></span>
            </div>
            <button class="cd-slider-btn next" onclick="changeStory(1)">❯</button>
        </div>
    </div>
</section>



<section class="internships-section">
    <div class="container">
        <h2 class="section-title">Career Opportunities</h2>
        <p class="section-subtitle">
            Join Our Leading IT Team to Innovate and Grow. Explore Exciting Career and Internship Opportunities with Us!
        </p>
        <div class="internships-grid">
            {% for i in intern %}
            <div class="internship-card">
                <img src="/media/{{i.background_image}}" loading="lazy" alt="internship photo">
                <h3>{{i.position}}</h3>
                <p>{{i.description | truncatechars:100}}</p>
                <a href="{% url 'applicant' i.id %}" class="apply-btn" target="_blank">Apply Now</a>
            </div>
            {% endfor %}
            {% for i in jobs %}
            <div class="internship-card">
                <img src="/media/{{i.background_image}}" loading="lazy" alt="job photo">
                <h3>{{i.position}}</h3>
                <p>{{i.description | truncatechars:100}}</p>
                <a href="{% url 'applicant' i.id %}" class="apply-btn" target="_blank">Apply Now</a>
            </div>
            {% endfor %}
        </div>
    </div>
    <a class="apply-btn" href="{% url 'careers' %}" style="margin-top: 20px; margin-bottom: 20px; float: right;">View All Opportunities</a>
</section>

<section class="cd-company-collab-section">
    <div class="cd-company-collab-container">
        <div class="cd-company-collab-content">
            <h2 class="cd-company-collab-title">
                Partner With Us – let's Grow Together</h2>
            <p class="cd-company-collab-description">
                Partner with Click Digitals to grow your business through smart digital solutions. We team up with innovative brands to deliver expert web development and result-driven digital marketing.
            </p>
            <a  class="btn btn-primary search-button" href="{% url 'collab' %}">click for more</a>
        </div>
        
    </div>
</section>

<section class="team-showcase" id="our-team">
    <div class="container">
        <h2 class="showcase-title">Our Amazing Team</h2>
        <p class="showcase-subtitle">Click Digitals Team – Skilled Minds Behind Your Digital Growth</p>
        
        <div class="team-cards">
            <div class="team-card" id="team-lead">
                <div class="card-image">
                    <img src="{% static 'images/mahesh.png' %}" loading="lazy" alt="Team Member">
                </div>
                <div class="card-content">
                    <h3>Mahesh das</h3>
                    <p class="role">CEO & Founder</p>
                    <p class="bio">Visionary leader with 5+ years of experience in digital transformation.</p>
                    <div class="social-media">
                        <a href="#" class="media-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="media-link"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>

            <div class="team-card" id="tech-lead">
                <div class="card-image">
                    <img src="{% static 'images/IMG_8249.JPG' %}" loading="lazy" alt="Team Member">
                </div>
                <div class="card-content">
                    <h3>Upasana Dahal</h3>
                    <p class="role"> Developer </p>
                    <p class="bio">Tech innovator specializing in scalable architecture and AI solutions.</p>
                    <div class="social-media">
                        <a href="https://www.linkedin.com/in/upasana-dahal-100064319?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app" class="media-link"><i class="fab fa-linkedin"></i></a>
                        <a href="https://github.com/UpasanaDahal" class="media-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>
            </div>

            <div class="team-card" id="dev-lead">
                <div class="card-image">
                    <img src="{% static 'images/dipesh.jpg' %}" loading="lazy" alt="Team Member">
                </div>
                <div class="card-content">
                    <h3>Dipesh Ghimire</h3>
                    <p class="role"> Developer</p>
                    <p class="bio">Full-stack expert passionate about creating efficient, user-friendly applications.</p>
                    <div class="social-media">
                        <a href="https://www.linkedin.com/in/dipesh8667/" class="media-link"><i class="fab fa-linkedin"></i></a>
                        <a href="https://github.com/dipesh867" class="media-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<footer>
  {% include "footer.html" %}   
</footer>

<script>
    let currentSlide = 1;

function showStory(n) {
let stories = document.querySelectorAll('.cd-success-card');
let dots = document.querySelectorAll('.cd-dot');

if (n > stories.length) { currentSlide = 1; }
if (n < 1) { currentSlide = stories.length; }

stories.forEach(story => {
story.classList.remove('active');
});

dots.forEach(dot => {
dot.classList.remove('active');
});

if (stories[currentSlide - 1]) {
stories[currentSlide - 1].classList.add('active');
}
if (dots[currentSlide - 1]) {
dots[currentSlide - 1].classList.add('active');
}
}

function changeStory(n) {
currentSlide += n;
showStory(currentSlide);
}

function currentStory(n) {
currentSlide = n;
showStory(currentSlide);
}

// Auto-play slider (optional)
setInterval(() => {
changeStory(1);
}, 4000);

// Add progress bar animations on scroll
window.addEventListener('scroll', () => {
const progressBars = document.querySelectorAll('.tech-progress');
progressBars.forEach(bar => {
const rect = bar.getBoundingClientRect();
if (rect.top < window.innerHeight && rect.bottom > 0) {
  const progress = bar.getAttribute('data-progress');
  bar.style.setProperty('--progress', progress + '%');
}
});
});
</script>
{% endblock   %}