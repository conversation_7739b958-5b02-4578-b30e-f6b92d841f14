<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Blog</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* Dark Theme Styles */
        body {
            background-color: #121212;
            color: #e0e0e0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #1e1e1e;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }

        h1 {
            color: #4e73df;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
        }

        h2 {
            color: #4e73df;
            margin-top: 0;
            font-size: 1.5rem;
        }

        .form-section {
            background-color: #252525;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 25px;
            border-left: 4px solid #4e73df;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #b0b0b0;
        }

        input[type="text"],
        input[type="number"],
        textarea,
        select {
            width: 96%;
            padding: 10px 15px;
            background-color: #2d2d2d;
            border: 1px solid #444;
            border-radius: 4px;
            color: #e0e0e0;
            font-size: 16px;
            transition: all 0.3s;
        }

        input[type="text"]:focus,
        input[type="number"]:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #4e73df;
            background-color: #3d3d3d;
        }

        .file-input-container {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-input-button {
            width: 100%;
            padding: 10px 15px;
            background-color: #2d2d2d;
            border: 1px solid #444;
            border-radius: 4px;
            color: #aaa;
            font-size: 16px;
            text-align: left;
            cursor: pointer;
        }

        .file-input-button:hover {
            background-color: #3d3d3d;
        }

        .file-input {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        /* Current file display */
        .current-file {
            margin-bottom: 10px;
            padding: 8px;
            background-color: #2d2d2d;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .current-file a {
            color: #4e73df;
            text-decoration: none;
        }

        .current-file a:hover {
            text-decoration: underline;
        }

        /* Keywords Styles */
        .keywords-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .keyword-tag {
            background-color: #4e73df;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .keyword-tag button {
            background: none;
            border: none;
            color: white;
            margin-left: 8px;
            cursor: pointer;
            padding: 0;
            font-size: 0.8rem;
        }

        /* Subtopic Styles */
        .subtopics-container {
            margin-top: 20px;
        }

        .subtopic-card {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 3px solid #4e73df;
            position: relative;
        }

        .subtopic-header {
            display: flex;
            margin-bottom: 10px;
        }

        .subtopic-title {
            flex-grow: 1;
            margin-right: 10px;
        }

        .delete-subtopic {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .delete-subtopic:hover {
            background-color: #c82333;
        }

        .add-subtopic {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
        }

        .add-subtopic:hover {
            background-color: #218838;
        }

        /* Submit Button */
        .submit-btn {
            background-color: #4e73df;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1rem;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            margin: 30px auto 0;
            transition: background-color 0.3s;
        }

        .submit-btn:hover {
            background-color: #3a5bbf;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .form-section {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Update Blog</h1>
        
        <form id="blogForm" enctype="multipart/form-data" action="{% url 'update_blog' blog.id %}" method="POST">
            {% csrf_token %}
            <input type="hidden" name="blog_id" value="{{ blog.id }}">
            
            <!-- Author Section -->
            <div class="form-section">
                <h2><i class="fas fa-user-edit"></i> Author Information</h2>
                <div class="form-group">
                    <label for="author_name">Author Name</label>
                    <input type="text" id="author_name" name="author_name" value="{{ author_name }}" required>
                </div>
                <div class="form-group">
                    <label for="author_description">Author Description</label>
                    <input type="text" id="author_description" name="author_description" value="{{ author_description }}" required>
                </div>
                <div class="form-group">
                    <label for="author_photo">Author Photo</label>
                    {% if blog.author.photo %}
                        <div class="current-file">
                            Current: <a href="{{ blog.author.photo.url }}" target="_blank">{{ blog.author.photo.name }}</a>
                        </div>
                    {% endif %}
                    <div class="file-input-container">
                        <button type="button" class="file-input-button" id="author_photo_label">Choose new file</button>
                        <input type="file" id="author_photo" name="author_photo" class="file-input" accept="image/*">
                    </div>
                </div>
            </div>
            
            <!-- Blog Information Section -->
            <div class="form-section">
                <h2><i class="fas fa-book-open"></i> Blog Information</h2>
                <div class="form-group">
                    <label for="blog_title">Blog Title</label>
                    <input type="text" id="blog_title" name="blog_title" value="{{ blog_title }}" required>
                </div>
                <div class="form-group">
                    <label for="blog_photo">Blog Cover Photo</label>
                    {% if blog.photo %}
                        <div class="current-file">
                            Current: <a href="{{ blog.photo.url }}" target="_blank">{{ blog.photo.name }}</a>
                        </div>
                    {% endif %}
                    <div class="file-input-container">
                        <button type="button" class="file-input-button" id="blog_photo_label">Choose new file</button>
                        <input type="file" id="blog_photo" name="blog_photo" class="file-input" accept="image/*">
                    </div>
                </div>
                <div class="form-group">
                    <label for="time_to_read">Estimated Reading Time (minutes)</label>
                    <input type="number" id="time_to_read" name="time_to_read" min="1" value="{{ time_to_read }}" required>
                </div>
                
                <!-- Keywords Section -->
                <div class="form-group">
                    <label for="keywords_input">Keywords (Press Enter to add)</label>
                    <input type="text" id="keywords_input" placeholder="Type keyword and press Enter">
                    <div id="keywords_container" class="keywords-container">
                        {% for keyword in keywords %}
                            <div class="keyword-tag">
                                {{ keyword }}
                                <button type="button" data-index="{{ forloop.counter0 }}">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        {% endfor %}
                    </div>
                    <input type="hidden" id="keywords" name="keywords" value="{{ keywords|join:',' }}">
                </div>
            </div>
            
            <!-- Subtopics Section -->
            <div class="form-section">
                <h2><i class="fas fa-list-ul"></i> Subtopics</h2>
                <div id="subtopics_container" class="subtopics-container">
                    {% for subtopic in subtopics %}
                        <div class="subtopic-card">
                            <div class="subtopic-header">
                                <input type="text" class="subtopic-title" placeholder="Subtopic Title" 
                                       value="{{ subtopic.title }}" required>
                                <button type="button" class="delete-subtopic">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <textarea class="subtopic-description" placeholder="Subtopic Description" 
                                      rows="4" required>{{ subtopic.description }}</textarea>
                        </div>
                    {% endfor %}
                </div>
                <button type="button" id="add_subtopic" class="add-subtopic">
                    <i class="fas fa-plus"></i> Add Subtopic
                </button>
            </div>
            
            <button type="submit" class="submit-btn">Update Blog</button>
        </form>
    </div>

    <!-- Subtopic Template (hidden) -->
    <div id="subtopic_template" style="display: none;">
        <div class="subtopic-card">
            <div class="subtopic-header">
                <input type="text" class="subtopic-title" placeholder="Subtopic Title" required>
                <button type="button" class="delete-subtopic">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <textarea class="subtopic-description" placeholder="Subtopic Description" rows="4" required></textarea>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize keywords array with existing keywords
            const keywords = JSON.parse(document.getElementById('keywords').value || '[]');
            const keywordsInput = document.getElementById('keywords_input');
            const keywordsContainer = document.getElementById('keywords_container');
            const keywordsHidden = document.getElementById('keywords');
            
            // Keywords functionality
            keywordsInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const keyword = this.value.trim();
                    if (keyword && !keywords.includes(keyword)) {
                        keywords.push(keyword);
                        updateKeywordsDisplay();
                        this.value = '';
                    }
                }
            });
            
            function updateKeywordsDisplay() {
                keywordsContainer.innerHTML = '';
                keywords.forEach((keyword, index) => {
                    const tag = document.createElement('div');
                    tag.className = 'keyword-tag';
                    tag.innerHTML = `
                        ${keyword}
                        <button type="button" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    keywordsContainer.appendChild(tag);
                });
                keywordsHidden.value = JSON.stringify(keywords);
            }
            
            // Initialize keywords display
            updateKeywordsDisplay();
            
            keywordsContainer.addEventListener('click', function(e) {
                if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                    const button = e.target.tagName === 'BUTTON' ? e.target : e.target.closest('button');
                    const index = parseInt(button.getAttribute('data-index'));
                    keywords.splice(index, 1);
                    updateKeywordsDisplay();
                }
            });
            
            // Subtopics functionality
            const subtopicsContainer = document.getElementById('subtopics_container');
            const subtopicTemplate = document.getElementById('subtopic_template').innerHTML;
            
            document.getElementById('add_subtopic').addEventListener('click', function() {
                const newSubtopic = document.createElement('div');
                newSubtopic.innerHTML = subtopicTemplate;
                subtopicsContainer.appendChild(newSubtopic);
            });
            
            subtopicsContainer.addEventListener('click', function(e) {
                if (e.target.classList.contains('delete-subtopic') || e.target.closest('.delete-subtopic')) {
                    const button = e.target.classList.contains('delete-subtopic') ? e.target : e.target.closest('.delete-subtopic');
                    button.closest('.subtopic-card').remove();
                }
            });
            
            // File input label update
            document.getElementById('author_photo').addEventListener('change', function() {
                const label = document.getElementById('author_photo_label');
                if (this.files.length > 0) {
                    label.textContent = this.files[0].name;
                } else {
                    label.textContent = 'Choose new file';
                }
            });
            
            document.getElementById('blog_photo').addEventListener('change', function() {
                const label = document.getElementById('blog_photo_label');
                if (this.files.length > 0) {
                    label.textContent = this.files[0].name;
                } else {
                    label.textContent = 'Choose new file';
                }
            });
            
            // Form submission handling
            document.getElementById('blogForm').addEventListener('submit', function(e) {
                // Collect all subtopics data
                const subtopics = [];
                const subtopicCards = document.querySelectorAll('.subtopic-card');
                
                subtopicCards.forEach(card => {
                    subtopics.push({
                        title: card.querySelector('.subtopic-title').value,
                        description: card.querySelector('.subtopic-description').value
                    });
                });
                
                // Add subtopics data to form as hidden input
                if (subtopics.length > 0) {
                    let input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'subtopics_json';
                    input.value = JSON.stringify(subtopics);
                    this.appendChild(input);
                }
            });
        });
    </script>
</body>
</html>