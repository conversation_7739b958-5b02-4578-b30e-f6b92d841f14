
.cd-contact-section {
    min-height: 100vh;
    padding: 80px 20px;
    background: linear-gradient(135deg, rgb(26, 30, 54), #0f3f69, rgb(7, 7, 74));
    position: relative;
    overflow: hidden;
}

/* Background Animation Effects */
.bg-animation-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.bg-animation-shape {
    position: absolute;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.05));
    border-radius: 50%;
    filter: blur(20px);
}

.shape-1 {
    width: 500px;
    height: 500px;
    top: -250px;
    right: -250px;
    animation: float 20s infinite alternate;
}

.shape-2 {
    width: 300px;
    height: 300px;
    bottom: -150px;
    left: -150px;
    animation: float 15s infinite alternate-reverse;
}

.shape-3 {
    width: 400px;
    height: 400px;
    top: 40%;
    right: 10%;
    border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
    animation: morph 15s infinite alternate;
}

@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    100% {
        transform: translateY(50px) rotate(10deg);
    }
}

@keyframes morph {
    0% {
        border-radius: 40% 60% 70% 30% / 40% 40% 60% 50%;
        transform: translate(0, 0) rotate(0deg);
    }
    100% {
        border-radius: 50% 50% 30% 70% / 30% 50% 50% 70%;
        transform: translate(20px, 20px) rotate(10deg);
    }
}

.cd-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.cd-section-title {
    padding-top: 50px;
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 15px;
    color: #fbf8f8;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    opacity: 2;
    transform: translateY(-20px);
    animation: fadeInDown 0.8s forwards 0.2s;
    position: relative;
}

.cd-section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #eb07ef, #057ce4);
    margin: 10px auto 0;
    border-radius: 4px;
    animation: expandWidth 1.5s forwards 0.8s;
    transform-origin: center;
    transform: scaleX(0);
}

@keyframes expandWidth {
    to {
        transform: scaleX(1);
    }
}

.cd-section-subtitle {
    text-align: center;
    color: #f7f1f1;
    margin-bottom: 50px;
    font-size: 1.2rem;
    opacity: 0;
    transform: translateY(-10px);
    animation: fadeInDown 0.8s forwards 0.4s;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

@keyframes fadeInDown {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cd-contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    transition: all 0.5s ease;
}

.cd-form-container,
.cd-info-container {
    padding: 35px;
    border-radius: 16px;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    background: linear-gradient(135deg, rgba(40, 105, 108, 0.9), rgba(45, 82, 114, 0.9), rgba(40, 40, 96, 0.9));
    backdrop-filter: blur(10px);
    transition: transform 0.4s ease, box-shadow 0.4s ease;
    opacity: 0;
    transform: translateY(30px);
}

.cd-form-container {
    animation: fadeInUp 0.8s forwards 0.6s;
}

.cd-info-container {
    animation: fadeInUp 0.8s forwards 0.8s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cd-form-container:hover,
.cd-info-container:hover {
    transform: translateY(-10px);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.cd-contact-form .cd-form-group {
    margin-bottom: 25px;
    position: relative;
    transition: all 0.3s ease;
}

.cd-contact-form label {
    display: block;
    font-weight: 600;
    margin-bottom: 10px;
    color: #f6f8f3;
    transition: all 0.3s ease;
    transform-origin: left;
    position: relative;
    padding-left: 12px;
}

.cd-contact-form label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 3px;
    height: 0;
    background: linear-gradient(to bottom, #6e126f, #0f3f69);
    transition: all 0.3s;
    border-radius: 3px;
    transform: translateY(-50%);
}

.cd-form-group:hover label::before {
    height: 70%;
}

.cd-contact-form input,
.cd-contact-form textarea,
.cd-contact-form select {
    width: 100%;
    padding: 14px 16px;
    border: 1px solid rgba(204, 204, 204, 0.3);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.08);
    color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cd-contact-form input:focus,
.cd-contact-form textarea:focus,
.cd-contact-form select:focus {
    border-color: #6e126f;
    outline: none;
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(110, 18, 111, 0.2);
    transform: translateY(-3px);
}

.cd-contact-form input::placeholder,
.cd-contact-form textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
    transition: all 0.3s;
}

.cd-contact-form input:focus::placeholder,
.cd-contact-form textarea:focus::placeholder {
    opacity: 0.5;
    transform: translateX(5px);
}

.cd-contact-form select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
    cursor: pointer;
}

.cd-contact-form select:hover {
    border-color: #6e126f;
}

.cd-contact-form select option {
    padding: 12px;
    background-color: #1f2b47;
    color: #fff;
}

.error-message {
    color: #f87171;
    font-size: 0.875rem;
    margin-top: 5px;
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.cd-btn {
    padding: 14px 28px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    z-index: 1;
}

.cd-btn-primary {
    background: linear-gradient(135deg, #6e126f, #9f1aa0);
    color: white;
    box-shadow: 0 8px 20px rgba(110, 18, 111, 0.3);
    transform: translateY(0);
}

.cd-btn-primary:hover {
    background: linear-gradient(135deg, #7c1480, #b01db1);
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(110, 18, 111, 0.4);
}

.cd-btn-primary:active {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(110, 18, 111, 0.4);
}

.cd-btn::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -60%;
    width: 20px;
    height: 200%;
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(30deg);
    transition: all 0.6s ease;
}

.cd-btn:hover::after {
    left: 110%;
}

.cd-info-container h3 {
    font-size: 1.8rem;
    margin-bottom: 25px;
    color: #e9e2e2;
    position: relative;
    display: inline-block;
}

.cd-info-container h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #6e126f, transparent);
    transition: width 0.3s ease;
}

.cd-info-container h3:hover::after {
    width: 100%;
}

.cd-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    transform: translateX(-20px);
    opacity: 0;
    animation: slideInRight 0.5s forwards;
}

.cd-info-item:nth-child(1) { animation-delay: 0.9s; }
.cd-info-item:nth-child(2) { animation-delay: 1.0s; }
.cd-info-item:nth-child(3) { animation-delay: 1.1s; }

@keyframes slideInRight {
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.info-icon {
    margin-right: 15px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #6e126f, #0f3f69);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(110, 18, 111, 0.3);
}

.info-icon i {
    color: white;
    font-size: 18px;
}

.info-content {
    flex: 1;
}

.info-content p {
    margin-bottom: 0;
    color: #f7f4f4;
    line-height: 1.6;
}

.info-content strong {
    display: block;
    margin-bottom: 3px;
    color: #fff;
    font-size: 1.05rem;
}

.cd-map-container {
    margin-top: 30px;
    border-radius: 12px;
    overflow: hidden;
    height: 250px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform: scale(0.95);
    opacity: 0;
    animation: scaleIn 0.8s forwards 1.2s;
}

@keyframes scaleIn {
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.cd-map-container iframe {
    width: 100%;
    height: 100%;
    border: none;
    transition: all 0.3s ease;
}

/* .social-icons {
    display: flex;
    gap: 15px;
    margin-top: 25px;
    opacity: 0;
    animation: fadeInUp 0.8s forwards 1.3s;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-icons a:hover {
    transform: translateY(-5px);
    background: linear-gradient(135deg, #6e126f, #0f3f69);
    box-shadow: 0 5px 15px rgba(110, 18, 111, 0.3);
}

.social-icons img {
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
} */

/* Responsiveness */
@media (max-width: 992px) {
    .cd-section-title {
        font-size: 2.4rem;
    }
    
    .cd-section-subtitle {
        font-size: 1.1rem;
    }
    
    .cd-form-container,
    .cd-info-container {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .cd-contact-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .cd-section-title {
        font-size: 2.2rem;
    }
    
    .cd-contact-section {
        padding: 60px 20px;
    }
    
    .cd-form-container,
    .cd-info-container {
        padding: 25px;
    }
    
    .cd-info-container {
        order: -1;
    }
}

@media (max-width: 480px) {
    .cd-section-title {
        font-size: 1.8rem;
    }
    
    .cd-section-subtitle {
        font-size: 1rem;
        margin-bottom: 30px;
    }
    
    .cd-contact-section {
        padding: 40px 15px;
    }
    
    .cd-form-container,
    .cd-info-container {
        padding: 20px;
        border-radius: 12px;
    }
    
    .cd-contact-form input,
    .cd-contact-form textarea,
    .cd-contact-form select {
        padding: 12px 14px;
        font-size: 0.95rem;
    }
    
    .cd-btn {
        padding: 12px 20px;
        width: 100%;
    }
    
    .social-icons {
        justify-content: center;
    }
}