{% extends 'base.html' %}
{% load static %}
{% block title %}
Deployed Projects 
{% endblock title %}
{% block content %}
<style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #0a0f1e, #1c2a3a, #2c3e50);
    color: white;
    min-height: 100vh;
  }

  .container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    margin-top: 100px;
  }

  .project-card {
    background: rgba(31, 44, 59, 0.85);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;
  }

  .project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(56, 189, 248, 0.15);
    border-color: rgba(56, 189, 248, 0.3);
  }

  .project-header {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    
  }

  .project-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .project-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .details-container {
    display: flex;
    gap: 20px;
    margin: 12px 0;
  }

  .client-info-section {
    background: linear-gradient(135deg, rgba(56, 189, 248, 0.1), rgba(56, 189, 248, 0.05));
    border: 1px solid rgba(56, 189, 248, 0.2);
    border-radius: 12px;
    padding: 12px 16px;
    position: relative;
    width: 350px;
    flex-shrink: 0;
  }

  .client-info-header {
    font-size: 1.1rem;
    color: #38bdf8;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(56, 189, 248, 0.3);
    font-weight: 600;
  }

  .client-info-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;
  }

  .client-info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #94a3b8;
    font-size: 0.9rem;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .client-info-item i {
    color: #38bdf8;
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
  }

  .client-info-item span {
    color: #fff;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .project-company {
    font-size: 0.95rem;
    color: #94a3b8;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-top: 6px;
    padding: 6px 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    width: fit-content;
    position: relative;
    overflow: hidden;
  }

  .project-company::before {
    content: '🏢';
    font-size: 1.1rem;
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.2));
    animation: float 3s ease-in-out infinite;
  }

  .project-company span {
    position: relative;
    z-index: 1;
    font-weight: 500;
  }

  .project-meta {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-end;
  }

  .meta-items {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
  }

  .days-remaining {
    font-size: 0.9rem;
    color: #94a3b8;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    white-space: nowrap;
  }

  .days-remaining i {
    color: #38bdf8;
  }

  .days-remaining.urgent {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
  }

  .days-remaining.urgent i {
    color: #ef4444;
  }

  .project-details {
    display: block;
    margin-bottom: 20px;
  }

  .company-section {
    width: 300px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
  }



  .info-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
  }

  .info-label {
    color: #94a3b8;
    font-size: 0.9rem;
    min-width: 100px;
  }

  .info-value {
    color: #fff;
    font-size: 0.9rem;
  }

  .assigned-employees {
    flex-grow: 1;
    background: rgba(31, 44, 59, 0.75);
    border-radius: 12px;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .assigned-title {
    font-size: 1.1rem;
    color: #38bdf8;
    margin-bottom: 15px;
    font-weight: 600;
  }

  .employee-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    width: 100%;
  }

  .employee-card {
    background: rgba(31, 44, 59, 0.75);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    min-height: 200px;
    position: relative;
    width: 100%;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .employee-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    border-color: rgba(56, 189, 248, 0.3);
  }

  .image-box {
    width: 85px;
    height: 85px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 15px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  .employee-card:hover .image-box {
    border-color: rgba(56, 189, 248, 0.3);
  }

  .image-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .card-header {
    text-align: center;
    width: 100%;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 5px;
  }

  .title {
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .employee-faculty {
    font-size: 0.85rem;
    color: #94a3b8;
    text-transform: capitalize;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .button-row {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    justify-content: flex-end;
  }

  .action-btn {
    border-radius: 8px;
    padding: 10px 15px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    background-color: #ef4444;
    border: 1px solid #ef4444;
  }

  .action-btn:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-2px);
  }

  @media (max-width: 1200px) {
    .employee-list {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 900px) {
    .employee-list {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 600px) {
    .employee-list {
      grid-template-columns: 1fr;
    }
  }

  .meta-info {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
  }

  .meta-info-item {
    font-size: 0.9rem;
    color: #94a3b8;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    white-space: nowrap;
  }

  .meta-info-item i {
    color: #38bdf8;
  }

  .meta-info-item.price {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  .meta-info-item.price i {
    color: #10b981;
  }

  .meta-info-item.faculty {
    background: rgba(56, 189, 248, 0.1);
    color: #38bdf8;
    border: 1px solid rgba(56, 189, 248, 0.2);
  }

  .meta-info-item.faculty i {
    color: #38bdf8;
  }

  .days-remaining.rating {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05));
    color: #ffd700;
    font-weight: 600;
    border: 1px solid rgba(255, 215, 0, 0.3);
    padding: 6px 12px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(255, 215, 0, 0.1);
    backdrop-filter: blur(4px);
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.95rem;
    line-height: 1;
    width: fit-content;
    min-width: auto;
  }

  .days-remaining.rating::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
      transparent, 
      rgba(255, 215, 0, 0.1), 
      rgba(255, 215, 0, 0.2), 
      rgba(255, 215, 0, 0.1), 
      transparent
    );
    animation: shimmer 3s infinite;
  }

  .days-remaining.rating::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
      circle at center,
      rgba(255, 215, 0, 0.1) 0%,
      transparent 70%
    );
    animation: pulse 4s infinite;
  }

  .days-remaining.rating i {
    color: #ffd700;
    font-size: 0.95rem;
    filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.3));
    display: inline-flex;
    align-items: center;
    line-height: 1;
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%) skewX(-15deg);
    }
    100% {
      transform: translateX(100%) skewX(-15deg);
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
    }
    100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-2px);
    }
    100% {
      transform: translateY(0px);
    }
  }
  .search-bar {
   
      width: 400px;
      max-width: 400px;
      border-radius: 5px;
      overflow: hidden;
      background-color: #1e1e1e;
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
    }
</style>
<style>
  .search-container {
    width: 400px;
    max-width: 400px;
    padding: 0 20px;
  }
  form {
    width: 100%;
  }
  input[type="search"] {
    width: 100%;
    padding: 14px 20px;
    border-radius: 10px;
    border: none;
    outline: none;
    font-size: 1.1rem;
    background-color: #435f7b;
    color: #eee;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
    transition: box-shadow 0.25s ease;
  }
  input[type="search"]::placeholder {
    color: #888;
  }
  input[type="search"]:focus {
    box-shadow: 0 0 15px #3f51b5;
    background-color: #2a2a2a;
    color: #fff;
  }
</style>


<div class="container">
  <div class="section-header">
    <h1 class="section-title">Deployed Project</h1>
  <div class="search-container">
    <form method="get" action=".">
    <input type="search" name="search" placeholder="Search..." value="{{ request.GET.search }}">
</form>

  </div>
  </div>
  <div class="projects-grid">
    {% if projects_data %}
    {% for data in projects_data %}
    <div class="project-card">
      <div class="project-header">
        <div class="project-info">
          <div class="meta-info">
            <div class="meta-info-item faculty">
              <i class="fas fa-graduation-cap"></i>
              {{ data.project.faculty }}
            </div>
            <div class="meta-info-item price">
              <i class="fas fa-dollar-sign"></i>
              ${{ data.project.price }}
            </div>
          </div>
          <h2 class="project-title">{{ data.project.title }}</h2>
          
        </div>
        <div class="project-meta">
          <div class="meta-items">
            <div class="days-remaining {% if 'ago' in data.days_text %}urgent{% endif %}">
              <i class="fas fa-clock"></i>
              {{ data.days_text }}
            </div>
            <div class="days-remaining rating">
              {{ data.project.rating }}<i class="fas fa-star"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="details-container">
        <div class="client-info-section">
          <div class="client-info-header">Client Details</div>
          <div class="client-info-grid">
            <div class="client-info-item">
              <i class="fas fa-building"></i>
              <span>{{ data.project.company_name }}</span>
            </div>
            <div class="client-info-item">
              <i class="fas fa-envelope"></i>
              <span>{{ data.project.email }}</span>
            </div>
            <div class="client-info-item">
              <i class="fas fa-phone"></i>
              <span>{{ data.project.contact }}</span>
            </div>
          </div>
        </div>

        <div class="assigned-employees">
          <div class="assigned-title">Assigned Employees</div>
          <div class="employee-list">
            {% for user in data.project.assigned_users.all %}
            <div class="employee-card">
              <div class="image-box">
                {% if user.image %}
                  <img src="{{ user.image.url }}" alt="{{ user.name }}" loading="lazy">
                {% else %}
                  <img src="{% static 'images/default-avatar.png' %}" alt="{{ user.name }}">
                {% endif %}
              </div>
              <div class="card-header">
                <h5 class="title">{{ user.name }}</h5>
                <div class="employee-faculty">{{ user.faculty }}</div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>

      <div class="button-row">
        <form method="post" action="{% url 'delete-deployed-project' data.project.id %}" style="margin:0;">
          {% csrf_token %}
          <button type="submit" class="action-btn">
            <i class="fas fa-trash"></i> Delete
          </button>
        </form>
      </div>
    </div>
    {% endfor %}
    {% else %}
      <div class="no-projects-message">
        <i class="fas fa-inbox fa-3x"></i>
        <h2>No Projects deployed</h2>
       
      </div>
    {% endif %}
  </div>
</div>
{% endblock %} 