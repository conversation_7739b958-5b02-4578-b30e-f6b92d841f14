/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f9f9f9;
  color: #333;
  overflow-x: hidden;
}

/* Main container */
.internship-page {
  margin-top: 0px;
  padding: 30px 20px;
  background: linear-gradient(135deg, rgb(5, 5, 47), rgb(102, 40, 40), rgb(4, 41, 4));
  min-height: 100vh;
  animation: fadeInBackground 1s ease-out;
}

@keyframes fadeInBackground {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Header section styling */
.internship-page-header {
  padding-top: 20px;
  max-width: 1200px;
  margin: 0 auto  auto;
  text-align: center;
  color: aliceblue;
  padding: 40px 20px;
  position: relative;
  overflow: hidden;
}

/* Animated background for header */
.internship-page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(135, 206, 235, 0.1) 0%, transparent 70%);
  animation: backgroundPulse 4s ease-in-out infinite;
  z-index: -1;
}

@keyframes backgroundPulse {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.1; }
  50% { transform: scale(1.1) rotate(180deg); opacity: 0.2; }
}

/* Main title styling */
.internship-title {
  font-size: clamp(28px, 5vw, 48px);
  margin-bottom: 20px;
  font-weight: 700;
  background: linear-gradient(45deg, #87ceeb, #20b2aa, #87ceeb);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 30px rgba(135, 206, 235, 0.3);
  position: relative;
  animation: slideInFromTop 1s ease-out, gradientShift 3s ease-in-out infinite;
}

/* Subtitle styling */
.internship-subtitle {
  font-size: clamp(16px, 3vw, 22px);
  color: #b1b0b0;
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  position: relative;
  animation: slideInFromBottom 1s ease-out 0.3s both;
  opacity: 0;
}

/* Slide in from top animation */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from bottom animation */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Gradient shift animation for title */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Decorative elements */
.internship-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, #87ceeb, #20b2aa, #87ceeb, transparent);
  border-radius: 2px;
  animation: expandLine 1.5s ease-out 0.5s both;
}

@keyframes expandLine {
  from { width: 0; }
  to { width: 80%; }
}

/* Floating particles effect */
.internship-page-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(135, 206, 235, 0.3) 1px, transparent 1px),
    radial-gradient(circle at 80% 40%, rgba(32, 178, 170, 0.3) 1px, transparent 1px),
    radial-gradient(circle at 40% 80%, rgba(135, 206, 235, 0.3) 1px, transparent 1px);
  animation: floatingParticles 8s linear infinite;
  z-index: -1;
}

@keyframes floatingParticles {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

/* Hover effects */
.internship-page-header:hover .internship-title {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.internship-page-header:hover .internship-subtitle {
  color: #d1d1d1;
  transition: color 0.3s ease;
}

/* Responsive design */
@media (max-width: 768px) {
  .internship-page {
    padding: 20px 10px;
    margin-top: 0px;
  }

  .internship-page-header {
    padding: 20px 15px;
    margin-bottom: 20px;
  }

  .internship-title {
    font-size: 28px;
    margin-bottom: 10px;
  }

  .internship-subtitle {
    font-size: 16px;
    padding: 0 10px;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 10px;
  }

  .benefit-card {
    padding: 20px;
  }

  .benefit-icon {
    font-size: 36px;
    margin-bottom: 15px;
  }

  .benefit-card h4 {
    font-size: 20px;
  }

  .benefit-card p {
    font-size: 14px;
  }

  .requirements-content {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 15px;
  }

  .highlight-text {
    font-size: 18px;
    margin-bottom: 20px;
  }

  .requirements-list li {
    font-size: 14px;
    margin-bottom: 12px;
    padding-left: 25px;
  }

  .process-timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
    padding-left: 70px;
    margin-bottom: 40px;
  }

  .timeline-dot {
    left: 30px;
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .timeline-content {
    width: calc(100% - 70px);
    padding: 20px;
  }

  .timeline-content h4 {
    font-size: 18px;
  }

  .timeline-content p {
    font-size: 14px;
  }

  .testimonials-container {
    padding: 0 15px;
  }

  .testimonial-card {
    padding: 25px;
  }

  .testimonial-content p {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .testimonial-author {
    flex-direction: column;
    gap: 15px;
  }

  .author-avatar {
    width: 50px;
    height: 50px;
  }

  .author-info h5 {
    font-size: 16px;
  }

  .author-info span {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .internship-page {
    padding: 40px 5px;
    margin-top: 0px;
  }

  .internship-page-header {
    padding: 40px 10px;
  }

  .internship-title {
    font-size: 24px;
  }

  .internship-subtitle {
    font-size: 14px;
  }

  .section-title {
    font-size: 20px;
    margin-bottom: 30px;
  }

  .benefit-card {
    padding: 15px;
  }

  .benefit-icon {
    font-size: 32px;
  }

  .benefit-card h4 {
    font-size: 18px;
  }

  .benefit-card p {
    font-size: 13px;
  }

  .requirements-content {
    padding: 0 10px;
  }

  .highlight-text {
    font-size: 16px;
  }

  .requirements-list li {
    font-size: 13px;
    padding-left: 22px;
  }

  .timeline-item {
    padding-left: 60px;
    margin-bottom: 30px;
  }

  .timeline-dot {
    width: 40px;
    height: 40px;
    font-size: 18px;
    left: 25px;
  }

  .timeline-content {
    width: calc(100% - 60px);
    padding: 15px;
  }

  .timeline-content h4 {
    font-size: 16px;
  }

  .timeline-content p {
    font-size: 13px;
  }

  .testimonial-card {
    padding: 20px;
  }

  .testimonial-content p {
    font-size: 14px;
  }

  .author-avatar {
    width: 40px;
    height: 40px;
  }

  .author-info h5 {
    font-size: 14px;
  }

  .author-info span {
    font-size: 11px;
  }

  .internship-item {
    padding: 15px;
  }

  .internship-image {
    height: 140px;
  }

  .internship-name {
    font-size: 18px;
  }

  .internship-description {
    font-size: 13px;
  }

  .btn-apply {
    padding: 10px 20px;
    font-size: 13px;
  }
}

/* Additional styles for very small devices */
@media (max-width: 320px) {
  .internship-page {
    padding: 10px 5px;
  }

  .internship-title {
    font-size: 22px;
  }

  .internship-subtitle {
    font-size: 13px;
  }

  .section-title {
    font-size: 18px;
  }

  .benefit-card {
    padding: 12px;
  }

  .benefit-icon {
    font-size: 28px;
  }

  .benefit-card h4 {
    font-size: 16px;
  }

  .benefit-card p {
    font-size: 12px;
  }

  .timeline-content {
    padding: 12px;
  }

  .timeline-content h4 {
    font-size: 15px;
  }

  .timeline-content p {
    font-size: 12px;
  }

  .testimonial-card {
    padding: 15px;
  }

  .testimonial-content p {
    font-size: 13px;
  }

  .internship-item {
    padding: 12px;
  }

  .internship-image {
    height: 120px;
  }

  .btn-apply {
    padding: 8px 16px;
    font-size: 12px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .internship-page-header::before,
  .internship-page-header::after,
  .internship-title {
    animation: none;
  }
  
  .internship-title,
  .internship-subtitle {
    opacity: 1;
    transform: none;
  }
}

/* Header section */
/* ... existing code ... */

/* New sections styling */

/* Benefits Section */
.internship-benefits {
  margin: 60px 0;
  padding: 0 20px;
}

.benefits-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  font-size: clamp(24px, 4vw, 36px);
  color: #fdfafa;
  margin-bottom: 40px;
  position: relative;
  animation: slideInFromBottom 0.8s ease-out;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #87ceeb, #20b2aa);
  border-radius: 2px;
  animation: expandLine 1s ease-out 0.5s both;
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandLine {
  from { width: 0; }
  to { width: 60px; }
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.benefit-card {
  background: rgba(24, 23, 24, 0.8);
  padding: 30px;
  border-radius: 16px;
  text-align: center;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: translateY(50px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.benefit-card:nth-child(1) { animation-delay: 0.1s; }
.benefit-card:nth-child(2) { animation-delay: 0.2s; }
.benefit-card:nth-child(3) { animation-delay: 0.3s; }
.benefit-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.benefit-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(135, 206, 235, 0.2);
}

.benefit-icon {
  font-size: 48px;
  margin-bottom: 20px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.benefit-card h4 {
  color: #87ceeb;
  font-size: 24px;
  margin-bottom: 15px;
  transition: color 0.3s ease;
}

.benefit-card:hover h4 {
  color: #20b2aa;
}

.benefit-card p {
  color: #cccccc;
  line-height: 1.6;
  font-size: 16px;
}

/* Process Section */
.internship-process {
  margin: 80px 0;
  padding: 0 20px;
  background: linear-gradient(135deg, #2c1c2a , #2b103e ,#2c1c2a);
}

.process-container {
  max-width: 1000px;
  margin: 0 auto;
}

.process-timeline {
  position: relative;
  margin-top: 60px;
}

.process-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #87ceeb, #20b2aa);
  animation: drawLine 2s ease-out;
}

@keyframes drawLine {
  from { height: 0; }
  to { height: 100%; }
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 60px;
  position: relative;
  opacity: 0;
  animation: slideInTimeline 0.8s ease-out forwards;
}

.timeline-item:nth-child(1) { animation-delay: 0.2s; }
.timeline-item:nth-child(2) { animation-delay: 0.4s; }
.timeline-item:nth-child(3) { animation-delay: 0.6s; }
.timeline-item:nth-child(4) { animation-delay: 0.8s; }

@keyframes slideInTimeline {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
  animation-name: slideInTimelineRight;
}

@keyframes slideInTimelineRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.timeline-dot {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #87ceeb, #20b2aa);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3); }
  50% { box-shadow: 0 4px 25px rgba(135, 206, 235, 0.6); }
  100% { box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3); }
}

.timeline-content {
  background: rgba(24, 23, 24, 0.9);
  padding: 30px;
  border-radius: 12px;
  width: 45%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(135, 206, 235, 0.2);
}

.timeline-content h4 {
  color: #87ceeb;
  font-size: 20px;
  margin-bottom: 10px;
}

.timeline-content p {
  color: #cccccc;
  line-height: 1.6;
}

/* Testimonials Section */
.internship-testimonials {
  margin: 80px 0;
  padding: 60px 20px;
  background: rgba(102, 40, 40, 0.2);
  backdrop-filter: blur(10px);
}

.testimonials-container {
  max-width: 800px;
  margin: 0 auto;
}

.testimonials-slider {
  margin-top: 50px;
  position: relative;
  overflow: hidden;
}

.testimonial-card {
  background: rgba(24, 23, 24, 0.9);
  padding: 40px;
  border-radius: 16px;
  text-align: center;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.5s ease;
  position: absolute;
  width: 100%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(135, 206, 235, 0.2);
}

.testimonial-card.active {
  opacity: 1;
  transform: translateX(0);
  position: relative;
}

.testimonial-content p {
  color: #fdfafa;
  font-size: clamp(16px, 3vw, 20px);
  font-style: italic;
  line-height: 1.8;
  margin-bottom: 30px;
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, #87ceeb, #20b2aa);
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { box-shadow: 0 0 20px rgba(135, 206, 235, 0.3); }
  50% { box-shadow: 0 0 30px rgba(135, 206, 235, 0.6); }
}

.author-info {
  text-align: left;
}

.author-info h5 {
  color: #87ceeb;
  font-size: 18px;
  margin-bottom: 5px;
}

.author-info span {
  color: #b1b0b0;
  font-size: 14px;
}

/* Grid container */
.internship-list-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
  gap: clamp(16px, 3vw, 32px);
  padding: 0 10px;
}

/* Individual internship cards */
.internship-item {
  background: linear-gradient(135deg, #2c1c2a , #2b103e ,#2c1c2a);
  border-radius: 10px;
  padding: 20px 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-align: center;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInCard 0.6s ease-out forwards;
  position: relative;
  overflow: hidden;
}

.internship-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 90%;
  height: 90%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.internship-item:hover::before {
  left: 100%;
}

@keyframes slideInCard {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.internship-item:nth-child(1) { animation-delay: 0.1s; }
.internship-item:nth-child(2) { animation-delay: 0.2s; }
.internship-item:nth-child(3) { animation-delay: 0.3s; }
.internship-item:nth-child(4) { animation-delay: 0.4s; }
.internship-item:nth-child(5) { animation-delay: 0.5s; }
.internship-item:nth-child(6) { animation-delay: 0.6s; }

.internship-item:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* Image styling */
.internship-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 12px;
  margin-bottom: 20px;
  transition: transform 0.4s ease;
  overflow: hidden;
}

.internship-item:hover .internship-image {
  transform: scale(1.1);
}

/* Text content */
.internship-name {
  font-size: clamp(18px, 4vw, 24px);
  margin: 15px 0;
  color: #fdfafa;
  font-weight: 600;
  transition: color 0.3s ease;
}

.internship-item:hover .internship-name {
  color: #87ceeb;
}

.internship-description {
  font-size: clamp(14px, 3vw, 16px);
  color: #cccccc;
  margin-bottom: 20px;
  line-height: 1.6;
  transition: color 0.3s ease;
}

.internship-item:hover .internship-description {
  color: #ffffff;
}

/* Apply button */
.btn-apply {
  padding: 12px 24px;
  background: linear-gradient(45deg, rgb(58, 48, 48), rgb(17, 13, 13), rgb(113, 19, 113));
  color: aqua;
  text-decoration: none;
  border-radius: 8px;
  display: inline-block;
  transition: all 0.3s ease;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.btn-apply::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(135, 206, 235, 0.3), transparent);
  transition: left 0.4s ease;
}

.btn-apply:hover::before {
  left: 100%;
}

.btn-apply:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(113, 19, 113, 0.4);
  background: linear-gradient(45deg, rgb(78, 68, 68), rgb(37, 33, 33), rgb(153, 39, 153));
}

/* Loading animation */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.internship-item:nth-child(none):hover {
  animation:  0.6s ease-in-out;
}

/* Smooth scrolling behavior */
html {
  scroll-behavior: smooth;
}

 