{% extends './templates/base.html' %}
{% block title %}
Update Employee
{% endblock title %}
{% block content %}

<style>
  .form-cont {
    position: relative;
    background: #1e2b3a;
    border-radius: 16px;
    padding: 20px 40px 40px 40px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6);
    width: 70%;
    margin: auto;
    color: white;
  }

  .close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    font-size: 2rem;
    font-weight: 700;
    color: #ccc;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
  }

  .close-btn:hover {
    color: #f55;
  }

  .form-header {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
    color: white;
  }

  .form-label {
    font-weight: 600;
    margin-bottom: 5px;
  }

  input.form-control, select.form-control {
    background-color: #2c3e50;  /* Dark slate */
    color: #ecf0f1;             /* Light text */
    padding: 10px;
    border: 1px solid #34495e;
    border-radius: 8px;
    margin-bottom: 10px;
    width: 100%;
    font-weight: 600;
    transition: box-shadow 0.3s ease, border-color 0.3s ease;
  }

  input.form-control::placeholder {
    color: #95a5a6; /* Muted gray */
  }

  input.form-control:focus, select.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 6px 2px rgba(52, 152, 219, 0.4);  /* Soft blue glow */
    background-color: #34495e;
    color: #ecf0f1;
  }

  .submit {
    background-color: #0ea5e9;
    border: none;
    color: white;
    border-radius: 8px;
    padding: 12px;
    font-weight: 600;
    font-size: 1.4rem;
    transition: all 0.3s ease;
    margin-top: 20px;
    width: 48%;
    cursor: pointer;
  }

  .cancel {
    background-color: #eb3434e8;
    border: none;
    color: white;
    border-radius: 8px;
    padding: 12px;
    font-weight: 600;
    font-size: 1.4rem;
    transition: all 0.3s ease;
    margin-top: 20px;
    width: 48%;
    cursor: pointer;
  }

  .submit:hover, .cancel:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-5px);
  }

  .button-row {
    display: flex;
    justify-content: space-between;
    gap: 8px;
  }
 
  #skills-list {
    position: absolute;
    background: #34495e;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.7);
    width: 100%;
    max-height: 160px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    color: white;
    margin-top: 2px;
  }

  .skill-option {
    padding: 8px 12px;
    cursor: pointer;
    user-select: none;
  }

  .skill-option:hover {
    background-color: #0ea5e9;
  }

  #selected-skills {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .selected-skill {
    background-color: #2980b9;
    padding: 6px 12px;
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    color: white;
    font-weight: 600;
    width: fit-content;
  }

  .remove-skill {
    cursor: pointer;
    margin-left: 10px;
    font-weight: bold;
    font-size: 18px;
    line-height: 18px;
  }

  .skill-input-container {
    position: relative;
  }

  @media (max-width: 768px) {
    .form-header {
      font-size: 1.5rem;
    }
    .submit, .cancel {
      width: 100%;
      margin-left: 0;
      margin-top: 10px;
    }
    .button-row {
      flex-direction: column;
      gap: 10px;
    }
  }
   input[type="file"].form-control {
  background-color: #2c3e50;  /* Same dark background */
  color: #ecf0f1;             /* Same light text */
  padding: 10px;
  border: 1px solid #34495e;
  border-radius: 8px;
  margin-bottom: 10px;
  width: 100%;
  font-weight: 600;
  box-sizing: border-box;
  cursor: pointer;
}

input[type="file"].form-control::file-selector-button {
  display: none; /* Hide the default button so whole input looks uniform */
}
</style>

<div class="container mt-5 form-cont">
  <!-- Cross button -->
  <button type="button" class="close-btn" aria-label="Close form">&times;</button>

  <div class="form-header">Update Employee</div>
  <form method="post" enctype="multipart/form-data">
    {% csrf_token %}
    <div class="row">
    <div class="col-md-6 mb-3">
      <label for="name" class="form-label">Name</label>
      <input type="text" id="name" name="name" value="{{employee.name}}" class="form-control" required>
    </div>
    <div class="col-md-6 mb-3">
        <label for="address" class="form-label">Address</label>
        <input type="text" id="address" name="address" value="{{employee.address}}" class="form-control" required>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6 mb-3">
        <label for="email" class="form-label">Email</label>
        <input type="email" id="email" name="email" value="{{employee.email}}" class="form-control" required>
      </div>
      <div class="col-md-6 mb-3">
        <label for="contact" class="form-label">Contact</label>
        <input type="number" id="contact" name="contact" value="{{employee.contact}}" class="form-control" required>
      </div>
    </div>

    <div class="row">
      <div class="col-md-4 mb-3">
        <label for="age" class="form-label">Age</label>
        <select id="age" name="age"  class="form-control" style="font-size: 16px; height: 40px;" >
          {% for i in ages %}
          <option value="{{ i }}" {% if employee.age == i %}selected{% endif %}>{{ i }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="col-md-4 mb-3">
        <label for="country" class="form-label">Country</label>
        <select id="country" name="country" class="form-control ">
          {%for country in countries%}
          <option value="{{country}}"{%if employee.country == country%}selected {%endif%}>{{country}}</option>
         {%endfor%}
        </select>
      </div>
      <div class="col-md-4 mb-3">
        <label for="experience" class="form-label">Experience (Years)</label>
        <select id="experience" name="experience" class="form-control">
          {% for i in  experience %}
          <option value="{{ i }}" {% if i.experience == year %}selected{% endif %}>{{ i }} </option>
          {% endfor %}
         
        </select>
      </div>
    </div>

    <div class="row">
      <div class="col-md-4 mb-3">
        <label for="salary" class="form-label">Salary</label>
        <input type="number" id="salary" name="salary" value="{{employee.salary}}" class="form-control" required>
      </div>
      <div class="col-md-4 mb-3">
        <label for="position" class="form-label">Position</label>
        <input type="text" id="position" name="position" value="{{employee.position}}" class="form-control" required>
      </div>
      <div class="col-md-4 mb-3">
        <label for="education" class="form-label">Education</label>
        <input type="text" id="education" name="education" value="{{employee.education}}" class="form-control" required>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6 mb-3">
        <label class="form-label">Gender</label>
        <div class="btn-group w-100" role="group">
          <input type="radio" class="btn-check" name="gender" id="male" value="male" autocomplete="off"{%if employee.gender == 'male'%} checked {%endif%} required>
          <label class="btn aa" for="male">Male</label>
          <input type="radio" class="btn-check" name="gender" id="female" value="female" autocomplete="off" {%if employee.gender == 'female'%} checked {%endif%} >
          <label class="btn aa" for="female">Female</label>
          <input type="radio" class="btn-check" name="gender" id="other" value="other" autocomplete="off" {%if employee.gender == 'others'%} checked {%endif%} >
          <label class="btn aa" for="other">Other</label>
        </div>
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label">Job Type</label>
        <div class="btn-group w-100" role="group">
          <input type="radio" class="btn-check" name="jobtype" id="parttime" value="partime" autocomplete="off"  {%if employee.job_type == 'parttime'%} checked {%endif%} required>
          <label class="btn aa" for="parttime">Part-Time</label>

          <input type="radio" class="btn-check" name="jobtype" id="fulltime" value="fulltime" autocomplete="off"  {%if employee.job_type == 'fulltime'%} checked {%endif%}>
          <label class="btn aa" for="fulltime">Full-Time</label>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6 mb-3">
        <label class="form-label">Faculty</label>
        <div class="btn-group w-100" role="group">
          <input type="radio" class="btn-check" name="faculty" id="development" value="development" autocomplete="off" {%if employee.faculty == 'development'%} checked {%endif%} required>
          <label class="btn aa" for="development">Development</label>
          
          <input type="radio" class="btn-check" name="faculty" id="digitalmarketing" value="digitalmarketing" autocomplete="off" {%if employee.faculty == 'digitalmarketing'%} checked {%endif%} >
          <label class="btn aa" for="digitalmarketing"> Digital Marketing</label>
        </div>
      </div>
      <div class="col-12 col-md-6">
                  <label class="form-label">Level</label>
                  <div class="btn-group w-100" role="group">
                    <input type="radio" class="btn-check" name="level" id="intern" value="intern" autocomplete="off" {% if employee.level == 'intern' %}checked{% endif %} required>
                    <label class="btn aa " for="intern">Intern</label>
        
                    <input type="radio" class="btn-check" name="level" id="junior" value="junior" autocomplete="off" {% if employee.level == 'junior' %}checked{% endif %}>
                    <label class="btn aa " for="junior">Junior</label>
        
                    <input type="radio" class="btn-check" name="level" id="senior" value="senior" autocomplete="off" {% if employee.level == 'senior' %}checked{% endif %}>
                    <label class="btn aa" for="senior">Senior</label>
                  </div>
                </div>
     </div>
    <div class="row">
      <div class="col-md-4 mb-3">
      <label for="image" class="form-label">Image</label>
      <input type="file" id="image" name="image" class="form-control" placeholder="select image" >
    </div>
    <div class="col-md-4 mb-3">
      <label for="name" class="form-label">Projects</label>
      <input type="number" id="rating" name="projects" value="{{employee.projects}}" class="form-control" required>
    </div>
    <div class="col-md-4 mb-3">
        <label for="address" class="form-label">Rating</label>
        <input type="number" id="rating" name="rating" value="{{employee.rating}}" class="form-control" required>
      </div>
    </div>

    <!-- Skills input -->
    <div class="mb-3 skill-input-container">
  <label for="skill-input" class="form-label">Skills (Select up to 10)</label>
  <input type="text" id="skill-input" class="form-control" autocomplete="off" placeholder="Type and press Enter to add skill" />
  <div id="skills-list"></div>
  <div id="selected-skills"></div>
  <input type="hidden" name="skills" id="final-skills" />
</div>

{{ existing_skills|json_script:"initial-skills" }}

    <!-- Buttons side by side -->
    <div class="button-row">
      <button type="submit" class="submit">Update Employee</button>
      <button type="button" class="cancel" onclick="window.history.back();">Cancel</button>
    </div>
  </form>
</div>



<script>
  // Skills dropdown data
  const skills = ['Python', 'JavaScript', 'Django', 'React', 'CSS', 'HTML', 'SQL', 'Java', 'C++', 'AWS'];

  const skillInput = document.getElementById('skill-input');
  const skillsList = document.getElementById('skills-list');
  const selectedSkillsDiv = document.getElementById('selected-skills');
  const finalSkillsInput = document.getElementById('final-skills');

  let selectedSkills = [];

  // Load existing skills from backend
  document.addEventListener('DOMContentLoaded', () => {
    const initialSkills = JSON.parse(document.getElementById('initial-skills').textContent);
    selectedSkills = initialSkills;
    updateSelectedSkills();
  });

  // Show filtered skills dropdown
  skillInput.addEventListener('input', () => {
    const query = skillInput.value.trim().toLowerCase();
    if (!query) {
      skillsList.style.display = 'none';
      return;
    }
    const filtered = skills.filter(s => s.toLowerCase().includes(query) && !selectedSkills.includes(s));
    if (filtered.length === 0) {
      skillsList.style.display = 'none';
      return;
    }
    skillsList.innerHTML = filtered.map(s => `<div class="skill-option">${s}</div>`).join('');
    skillsList.style.display = 'block';

    // Add click event to each skill option
    document.querySelectorAll('.skill-option').forEach(el => {
      el.addEventListener('click', () => {
        addSkill(el.textContent);
        skillInput.value = '';
        skillsList.style.display = 'none';
      });
    });
  });

  // Hide dropdown when clicking outside
  document.addEventListener('click', (e) => {
    if (!skillInput.contains(e.target) && !skillsList.contains(e.target)) {
      skillsList.style.display = 'none';
    }
  });

  // Add skill function
  function addSkill(skill) {
    if (selectedSkills.length >= 10) {
      alert('You can select up to 10 skills only.');
      return;
    }
    if (!selectedSkills.includes(skill)) {
      selectedSkills.push(skill);
      updateSelectedSkills();
    }
  }

  // Remove skill function
  function removeSkill(skill) {
    selectedSkills = selectedSkills.filter(s => s !== skill);
    updateSelectedSkills();
  }

  // Update displayed selected skills and hidden input
  function updateSelectedSkills() {
    selectedSkillsDiv.innerHTML = selectedSkills.map(skill =>
      `<div class="selected-skill">${skill} <span class="remove-skill" onclick="removeSkill('${skill}')">&times;</span></div>`
    ).join('');
    finalSkillsInput.value = selectedSkills.join(',');
  }

  // Add skill on Enter key press
  skillInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const skill = skillInput.value.trim();
      if (skill && !selectedSkills.includes(skill)) {
        addSkill(skill);
        skillInput.value = '';
        skillsList.style.display = 'none';
      }
    }
  });

  // Close button behavior
  const closeBtn = document.querySelector('.close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', () => {
      window.history.back();
    });
  }
</script>
{% endblock %}
