# from django.db import models


    
# class services(models.Model):
#     image = models.ImageField( upload_to='media/', max_length=100)    
#     Title = models.CharField( max_length=150)
#     description = models.CharField(max_length=500)


# class Contact(models.Model):
#     name = models.CharField(max_length=100)
#     email = models.EmailField()
#     phone = models.CharField(max_length=20, blank=True)
#     subject = models.CharField(max_length=100)
#     message = models.TextField()
#     created_at = models.DateTimeField(auto_now_add=True)

#     def __str__(self):
#         return f"{self.name} - {self.subject}"

# class Collaboration(models.Model):
#     company_name = models.CharField(max_length=100)
#     company_email = models.EmailField()
#     industry_type= models.CharField(max_length=100)
#     message = models.TextField()

#     def __str__(self):
#         return self.company_name


