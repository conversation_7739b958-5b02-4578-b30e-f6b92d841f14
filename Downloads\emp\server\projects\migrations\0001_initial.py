# Generated by Django 5.1.2 on 2025-06-13 02:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('myapp', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Characterstics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Pastprojects',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=100)),
                ('contact', models.IntegerField()),
                ('title', models.CharField(max_length=100)),
                ('company_name', models.CharField(max_length=100)),
                ('faculty', models.CharField(choices=[('development', 'Development'), ('digital marketing', 'Digital Marketing')], max_length=100)),
                ('price', models.IntegerField()),
                ('deadline', models.DateField()),
                ('rating', models.IntegerField(default=0)),
                ('assigned_users', models.ManyToManyField(related_name='past_projects', to='myapp.employee')),
            ],
        ),
        migrations.CreateModel(
            name='Services',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_name', models.CharField(max_length=100)),
                ('faculty', models.CharField(choices=[('development', 'Development'), ('digitalmarketing', 'Digital Marketing')], max_length=100)),
                ('price', models.IntegerField()),
                ('characterstics', models.ManyToManyField(blank=True, to='projects.characterstics')),
            ],
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(max_length=200)),
                ('company_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=100)),
                ('contact', models.BigIntegerField()),
                ('servicedetails', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project', to='projects.services')),
            ],
        ),
        migrations.CreateModel(
            name='Startproject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(max_length=200)),
                ('price', models.IntegerField()),
                ('deadline', models.DateField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('started', 'Started'), ('completed', 'Completed')], max_length=100)),
                ('assigned_users', models.ManyToManyField(related_name='assigned_projects', to='myapp.employee')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='startproject', to='projects.project')),
            ],
        ),
    ]
