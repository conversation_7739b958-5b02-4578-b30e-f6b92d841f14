{% extends "header.html" %}
{% block content %}

  <section class="cd-collab-form-section">
        <div class="bg-animation">
            <!-- Generated by JS -->
        </div>
        
        <div class="cd-collab-form-container">
            <div class="form-left">
                <h1 class="cd-collab-form-title">Company Information</h1>
                <p class="form-subtitle">Join our network of collaborators and grow your business with us. Fill out the form to get started on your journey.</p>
                <div class="form-glow"></div>
            </div>
            
            <div class="form-right">
                

                <form class="cd-collab-form" method="POST" id="collabForm" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="form-row">
                        <div class="cd-form-group">
                            <label for="company-name">Company Name</label>
                            <input type="text" id="company-name" name="company_name" placeholder="Enter your company name" required>
                        </div>
                        <div class="cd-form-group">
                            <label for="industry">Industry</label>
                            <input type="text" id="industry" name="industry" placeholder="Enter your company type" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="cd-form-group">
                            <label for="collab-email">Email</label>
                            <input type="email" id="collab-email" name="email" placeholder="Enter your email address" required>
                        </div>
                        <div class="cd-form-group">
                            <label for="collab-contact">Contact</label>
                            <input type="contact" id="collab-contact" name="contact" placeholder="Enter your Contact number" required>
                        </div>
                    </div>
                    
                    
                    <div class="cd-form-group">
                        <label for="collab-description">Proposal or Message</label>
                        <textarea id="collab-description" name="description" rows="5" placeholder="Share your collaboration idea..." required></textarea>
                    </div>
                    
                    <button type="submit" class="cd-btn cd-btn-primary">Submit Proposal</button>
                </form>
            </div>   
                                 {% if messages %}
                    <div class="messages" style="color:azure">
                        {% for message in messages %}
                        <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
        </div>
    </section>

    <script>
        // Create background animation elements
        document.addEventListener('DOMContentLoaded', function() {
            const bgAnimation = document.querySelector('.bg-animation');
            
            // Create more varied floating particles
            for (let i = 0; i < 50; i++) {
                const span = document.createElement('span');
                const size = Math.random() * 30 + 5;
                
                span.style.width = size + 'px';
                span.style.height = size + 'px';
                span.style.left = Math.random() * 100 + '%';
                span.style.top = Math.random() * 100 + '%';
                span.style.animationDelay = Math.random() * 5 + 's';
                span.style.animationDuration = Math.random() * 10 + 5 + 's';
                span.style.opacity = Math.random() * 0.5;
                span.style.filter = `blur(${Math.random() * 4}px)`;
                
                if (i % 4 === 0) {
                    span.style.background = 'rgba(102, 126, 234, 0.1)';
                } else if (i % 4 === 1) {
                    span.style.background = 'rgba(66, 153, 225, 0.1)';
                    span.style.borderRadius = '30% 70% 70% 30% / 30% 30% 70% 70%';
                } else if (i % 4 === 2) {
                    span.style.background = 'rgba(236, 201, 75, 0.1)';
                    span.style.borderRadius = '50% 50% 20% 80% / 25% 80% 20% 75%';
                } else {
                    span.style.background = 'rgba(245, 101, 101, 0.1)';
                    span.style.borderRadius = '80% 20% 50% 50% / 50% 40% 60% 50%';
                }
                
                bgAnimation.appendChild(span);
            }
            
            // Form submission handling
            const form = document.getElementById('collabForm');
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.textContent = 'Submitting...';
            });
        });
    </script>
   
    <style>
        .messages {
            background:linear-gradient(to right, #00b09b, #96c93d);
            position: fixed;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-top: 10px;
            max-width: 350px;
            z-index: 1000;

        }

        .alert {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.5s ease-out;
        }

        .alert-success {
            background-color: #4CAF50;
            color: white;
            border-left: 4px solid #45a049;
        }

        .alert-error {
            background-color: #f44336;
            color: white;
            border-left: 4px solid #d32f2f;
        }

        .alert-warning {
            background-color: #ff9800;
            color: white;
            border-left: 4px solid #f57c00;
        }

        .alert-info {
            background-color: #2196F3;
            color: white;
            border-left: 4px solid #2196F3;
        }

        .alert-primary {
            background-color: #007bff;
            color: white;
            border-left: 4px solid #007bff;
        }
</style>
{% endblock %}
