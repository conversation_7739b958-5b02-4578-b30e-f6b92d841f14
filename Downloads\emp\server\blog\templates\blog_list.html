<style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #0a0f1e, #1c2a3a, #2c3e50);
    color: white;
  }

  .container {
    padding-bottom: 60px;
    margin-top: 20px;
  }


  .card-header:hover .title {
    color: #7ed6df;
  }


a{
    text-decoration: none;
}
.author-card, .table-of-contents, .related-posts {
  background: rgba(255,255,255,0.05);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.author-card:hover, .related-posts:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.author-card .author-image {
  width: 80px;
  height: 80px;
  margin-bottom: 1rem;
}

.author-card h3 {
  margin-bottom: 1rem;
}

.author-card p {
  color: #b0b0b0;
  margin-bottom: 1rem;
}
.related-posts {
  background: rgba(255,255,255,0.05);
  border-radius: 20px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
      display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.related-posts h3 {
  margin-bottom: 0.8rem;
  color:white;
  font-size: 1.2rem;
}

.related-post-card {
  display: flex;
  gap: 1rem;
  padding: 0.5rem 0 ;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.related-post-card img {
  
        object-position: center;
        width: 70px;
  height: 60px;
 
  border-radius: 10px;
       
}

.related-post-card h4 {
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
}
.related-post-content h4{
    color:white;
}
.related-post-card .post-date {
  font-size: 0.8rem;
}
.post-date{
    color:gray;
}
  .close-btn {
    position: absolute;
    background: transparent;
    border: none;
    font-weight: 700;
    color: #ccc;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
  }
  
    .submit {
    background-color: #0ea5e9;
    border: none;
    color: white;
    border-radius: 8px;
    padding: 8px;
    font-weight: 600;
    font-size: 1rem;
    transition: background-color 0.3s ease, 0.3s transform ease;
    width: 48%;
    cursor: pointer;
  }

  .submit:hover {
    background-color: #2a2f3b;
    border-color: #3498db;
    box-shadow: 0 0 6px 2px rgba(52, 152, 219, 0.4);
    transform:translateY(-5px)
  }

  .cancel {
    background-color: #eb3434e8;
    border: none;
    color: white;
    border-radius: 8px;
    padding: 8px;
    font-weight: 600;
    font-size: 1rem;
    transition: background-color 0.3s ease, 0.3s transform ease;
    margin-top: 0px;
    width: 48%;
    cursor: pointer;
  }

  .cancel:hover {
    background-color: #2a2f3b;
    border-color: #3498db;
    box-shadow: 0 0 6px 2px rgba(52, 152, 219, 0.4);
    transform:translateY(-5px)
  }
  .button-row{
    margin-top: 15px;
    width: 100%;
    display: flex;
    gap: 5%;
  }
 

</style>

{% extends './templates/base.html' %}

{% block title %}
  Blog List
{% endblock title %}
{% load static %}
{% block content %}
<div class="container">
  <div class="section-header">
    <h1 class="section-title">Blog List</h1>
    <a href="{% url 'create' %}" class="add-service-btn">
      <i class="fas fa-plus"></i> Add Blog
    </a>
  </div>
  
  <div class="row g-4 mt-5">
    {%if blogs%}
    {% for blog in blogs %} 
      <div class="col-12 col-sm-6 col-md-4 col-lg-3 "> 
        
            <div class="related-posts">
              <h3>{{blog.title}}</h3>
              <div class="related-post-card">
                <img src="/media/{{blog.author.photo}}" alt="Related Post">
                <div class="related-post-content">
                  <h4>{{blog.author.name}}</h4>
                  <span class="post-date">{{blog.published_date}}</span>
                </div>
              </div>
              
           <div class="button-row">
      <a href="{% url 'update_blog' blog.id %}"  class="submit">Update</a>
      <a href="{% url 'delete_blog' blog.id %}" class="cancel">delete</a>
            </div>
            
      </div>
      </div>
     {% endfor %}
     
     {% else %}
      <div class="no-projects-message">
        <i class="fas fa-inbox fa-3x"></i>
        <h2>No Blogs Published yet</h2>
      </div>
    {% endif %} 
  
</div>
{% endblock %}




    

