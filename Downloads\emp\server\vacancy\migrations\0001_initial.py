# Generated by Django 5.1.2 on 2025-06-13 02:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Applicantskill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='GenerateVacancy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_type', models.CharField(choices=[('parttime', 'Part Time'), ('fulltime', 'Full Time')], default='fulltime', max_length=20)),
                ('faculty', models.CharField(choices=[('development', 'Development'), ('digitalmarketing', 'Digital Marketing')], default='development', max_length=20)),
                ('level', models.<PERSON>r<PERSON><PERSON>(choices=[('intern', 'Intern'), ('junior', 'Junior'), ('senior', 'Senior')], default='intern', max_length=20)),
                ('position', models.CharField(max_length=50)),
                ('description', models.CharField(max_length=500)),
                ('task', models.CharField(max_length=100)),
                ('background_image', models.ImageField(upload_to='vacancy-background')),
            ],
        ),
        migrations.CreateModel(
            name='Applicant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('age', models.IntegerField(default=20)),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female'), ('others', 'Others')], max_length=10)),
                ('schedule', models.CharField(choices=[('scheduled', 'Scheduled'), ('pending', 'Pending')], default='pending', max_length=20)),
                ('image', models.ImageField(upload_to='applicant-image')),
                ('resume', models.FileField(upload_to='resume')),
                ('email', models.EmailField(max_length=100)),
                ('country', models.TextField(max_length=50)),
                ('address', models.TextField(max_length=50)),
                ('contact', models.CharField(max_length=15)),
                ('experience', models.IntegerField(default=0)),
                ('education', models.CharField(default='not specified', max_length=100)),
                ('schedule_date', models.DateField(blank=True, null=True)),
                ('skills', models.ManyToManyField(blank=True, to='vacancy.applicantskill')),
                ('vacancydetails', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vacancy', to='vacancy.generatevacancy')),
            ],
        ),
        migrations.CreateModel(
            name='PostInterview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(default=0)),
                ('filtered_applicant', models.ManyToManyField(related_name='interview', to='vacancy.applicant')),
            ],
        ),
    ]
