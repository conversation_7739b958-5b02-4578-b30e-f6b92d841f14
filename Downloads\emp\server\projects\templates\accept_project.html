{% extends 'base.html' %}
{% load static %}
{% block title %}
Project
{% endblock title %}
{% block content %}
<style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #0a0f1e, #1c2a3a, #2c3e50);
    color: white;
    min-height: 100vh;
  }

  .container {
    padding: 20px;
    max-width: 1400px;
  }

  .section-header {
    margin-top: 40px;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  

  .section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #38bdf8;
    margin-bottom: 15px;
    text-align: left;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
  }

  .section-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 0;
    height: 2px;
    background-color: #38bdf8;
    transition: width 0.6s ease;
  }

  .assigned-employees:hover .section-title::after,
  .available-employees:hover .section-title::after {
    width: 100%;
  }

  .project-card {
    background: rgba(31, 44, 59, 0.85);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    margin: 20px auto;
    width: 95%;
  }

  .project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(56, 189, 248, 0.15);
    border-color: rgba(56, 189, 248, 0.3);
  }

  .project-info {
    border-right: 2px solid rgba(255, 255, 255, 0.1);
    padding-right: 20px;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 15px;
  }

  .info-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .info-item.editable {
    cursor: pointer;
  }

  .info-item.editable:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .info-item.project-name {
    background: transparent;
    padding: 0;
    margin-bottom: 20px;
  }

  .info-item.project-name .info-value {
    font-size: 2.2rem;
    font-weight: 700;
    color: white;
    line-height: 1.2;
    text-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
  }

  .project-details {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .faculty-badge {
    background: rgba(14, 165, 233, 0.15);
    color: #38bdf8;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 12px;
    border: 1px solid rgba(56, 189, 248, 0.3);
  }

  .project-title {
    font-size: 1.6rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .contact-email-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
  }

  .contact-email-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .info-label {
    font-size: 0.85rem;
    color: #94a3b8;
    margin-bottom: 6px;
    font-weight: 500;
  }

  .info-value {
    font-size: 0.95rem;
    color: #ffffff;
    word-break: break-word;
    line-height: 1.4;
  }

  .info-value input,
  .info-value textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    color: white;
    font-size: 0.95rem;
    transition: all 0.3s ease;
  }

  .info-value input:focus,
  .info-value textarea:focus {
    outline: none;
    border-color: #38bdf8;
    box-shadow: 0 0 10px rgba(56, 189, 248, 0.3);
  }

  .project-description {
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    height: 290px;
    max-height: 290px;
    min-height: 290px;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 15px;
    flex: none;
  }

  .project-description:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .description-title {
    font-size: 1.1rem;
    color: #38bdf8;
    margin-bottom: 10px;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .description-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    min-height: 200px;
    max-height: 200px;
  }

  .description-content textarea {
    width: 100%;
    height: 200px;
    max-height: 200px;
    min-height: 200px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    color: white;
    font-size: 0.95rem;
    resize: none;
    overflow-y: auto;
    transition: all 0.3s ease;
  }

  .description-content textarea:hover,
  .description-content textarea:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: #38bdf8;
    box-shadow: 0 0 10px rgba(56, 189, 248, 0.3);
    outline: none;
  }

  .button-row {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    margin-top: 15px;
  }

  .action-btn {
    width: 48%;
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: white;
  }

  .btn-finalize {
    background-color: #10b981;
    border: 1px solid #10b981;
  }

  .btn-cancel {
    background-color: #dc2626;
    border: 1px solid #dc2626;
  }

  .action-btn:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-5px);
  }

  .action-btn i {
    font-size: 1.2rem;
  }

  @media (max-width: 992px) {
    .project-card {
      grid-template-columns: 1fr;
    }

    .project-info {
      border-right: none;
      border-bottom: 2px solid rgba(255, 255, 255, 0.1);
      padding-right: 0;
      padding-bottom: 15px;
    }

    .contact-email-section {
      flex-direction: column;
    }

    .project-description {
      min-height: 200px;
    }
  }

  @media (max-width: 768px) {
    .info-grid {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 576px) {
    .project-card {
      padding: 15px;
    }

    .button-row {
      flex-direction: column;
    }

    .action-btn {
      width: 100%;
    }
  }

  .form-select {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    color: white;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
  }

  .form-select:focus {
    outline: none;
    border-color: #38bdf8;
    box-shadow: 0 0 10px rgba(56, 189, 248, 0.3);
  }

  .form-select option {
    background-color: #1e2b3a;
    color: white;
  }

  .option-group {
    display: flex;
    justify-content: space-between;
    width: 100%;
    border: 1px solid #384e63;
    border-radius: 8px;
    overflow: hidden;
  }

  .option-group input[type="radio"] {
    display: none;
  }

  .option-group label {
    flex: 1;
    text-align: center;
    padding: 8px 6px;
    font-weight: 600;
    background-color: #2a2f3b;
    color: #e0e6f1;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
    user-select: none;
  }

  .option-group label:not(:last-child) {
    border-right: 1px solid #384e63;
  }

  .option-group label:hover {
    background-color: #38bdf8;
    color: white;
  }

  .option-group input[type="radio"]:checked + label {
    background-color: #0ea5e9;
    color: white;
  }

  .project-description.editable {
    cursor: pointer;
  }

  .project-description.editable:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .employee-section {
    margin-top: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
  }

  .employee-section .section-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #38bdf8;
    margin-bottom: 20px;
    text-align: left;
  }

  .employees-container {
    display: block;
    width: 100%;
  }

  .assigned-employees {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 30px;
  }

  .available-employees {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: 20px;
    width: 100%;
    min-height: 200px;
  }

  .assigned-employees .employees-grid,
  .available-employees .employees-grid {
    margin-top: 15px;
    display: grid;
    gap: 15px;
  }

  .assigned-employees .employees-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .available-employees .employees-grid {
    grid-template-columns: repeat(4, 1fr);
    width: 100%;
  }

  .available-employees .employees-grid:empty {
    min-height: 100px;
    display: grid;
    place-items: center;
    position: relative;
  }

  .available-employees .employees-grid:empty::after {
    content: 'No available employees';
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.9rem;
  }

  .ggg {
    background: rgba(31, 44, 59, 0.75);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
    padding: 15px;
    transition: all 0.35s ease;
    border: 1px solid rgba(255, 255, 255, 0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 180px;
    min-height: 180px;
    position: relative;
  }

  .ggg:hover {
    transform: translateY(-8px);
    box-shadow: 0 18px 45px rgba(0, 0, 0, 0.6);
  }

  .image-box {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 12px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    flex-shrink: 0;
  }

  .ggg:hover .image-box {
    border-color: #38bdf8;
    transform: scale(1.05);
  }

  .image-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .card-header {
    text-align: center;
    padding-bottom: 8px;
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 45px;
  }

  .title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 4px 0;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: color 0.3s;
    line-height: 1.2;
  }

  .ggg:hover .title {
    color: #38bdf8;
  }

  .employee-faculty {
    font-size: 0.8rem;
    color: #94a3b8;
    text-transform: capitalize;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
  }

  @media (max-width: 1200px) {
    .available-employees .employees-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 992px) {
    .available-employees .employees-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 768px) {
    .available-employees .employees-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .assigned-employees .employees-grid {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 576px) {
    .available-employees .employees-grid {
      grid-template-columns: 1fr;
    }
  }

  .project-description.editable {
    cursor: pointer;
  }

  .project-description.editable:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .description-content textarea::-webkit-scrollbar {
    width: 8px;
  }

  .description-content textarea::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  .description-content textarea::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  .description-content textarea::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .remove-employee {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    color: #fff;
    transition: all 0.3s ease;
  }

  .remove-employee:hover {
    background: rgba(220, 38, 38, 0.8);
    transform: scale(1.1);
  }

  .ggg {
    position: relative;
  }

  .available-employee {
    cursor: pointer;
  }

  .available-employee:hover {
    border-color: #38bdf8;
  }

  .assigned-employees .section-title,
  .available-employees .section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #38bdf8;
    margin-bottom: 15px;
    text-align: left;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
  }

  .assigned-employees .section-title::after,
  .available-employees .section-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 0;
    height: 2px;
    background-color: #38bdf8;
    transition: width 0.6s ease;
  }

  .assigned-employees:hover .section-title::after,
  .available-employees:hover .section-title::after {
    width: 100%;
  }
  .container{
    margin-top: 50px;
  }
</style>

<div class="container">
  <div class="section-header">
    <!-- <a href="javascript:history.back()" class="back-arrow">
      <i class="fas fa-arrow-left"></i>
    </a> -->
    <h1 class="section-title">Project Distribution</h1>
  </div>

  <form method="POST" class="project-card">
    {% csrf_token %}
    <div class="project-info">
      <div class="faculty-badge">{{ project.servicedetails.faculty }}</div>
      <div class="info-grid">
        <div class="info-item project-name">
          <div class="info-value">{{ project.name }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Company</div>
          <div class="info-value">{{ project.company_name }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Status</div>
          <div class="info-value">
            <div class="option-group">
              <input type="radio" id="pending" name="status" value="pending" required>
              <label for="pending">Pending</label>

              <input type="radio" id="started" name="status" value="started">
              <label for="started">Started</label>

              <input type="radio" id="completed" name="status" value="completed">
              <label for="completed">Completed</label>
            </div>
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">Deadline</div>
          <div class="info-value">
            <input type="date" name="deadline" required>
          </div>
        </div>
      </div>

      <div class="assigned-employees">
        <div class="section-title">Assigned Employees</div>
        <div class="employees-grid" id="assignedEmployeesList">
          <!-- Remove the for loop since this is a new project -->
        </div>
        <input type="hidden" name="assigned_users" id="assignedEmployeesInput" value="">
      </div>

      <div class="button-row">
        <button type="submit" class="action-btn btn-finalize">
          <i class="fas fa-check-circle"></i>
          Finalize
        </button>
        <button type="button" class="action-btn btn-cancel" onclick="history.back()">
          <i class="fas fa-times-circle"></i>
          Cancel
        </button>
      </div>
    </div>

    <div class="project-details">
      <div class="contact-email-section">
        <div class="contact-email-item editable">
          <div class="info-label">Contact</div>
          <div class="info-value">
            <input type="text" name="contact" value="{{ project.contact }}" required>
          </div>
        </div>
        <div class="contact-email-item editable">
          <div class="info-label">Email</div>
          <div class="info-value">
            <input type="email" name="email" value="{{ project.email }}" required>
          </div>
        </div>
        <div class="contact-email-item editable">
          <div class="info-label">Price</div>
          <div class="info-value">
            <input type="number" name="price" value="{{ project.servicedetails.price }}" required>
          </div>
        </div>
      </div>

      <div class="project-description editable">
        <div class="description-title">Project Description</div>
        <div class="description-content">
          <textarea name="description" required>{{ project.description }}</textarea>
        </div>
      </div>

      <div class="employees-container">
        <div class="available-employees">
          <div class="section-title">Available Employees</div>
          <div class="employees-grid">
            {% for user in available_users %}
            <div class="col-12">
              <div class="ggg available-employee" 
                   data-employee-id="{{ user.id }}"
                   data-employee-name="{{ user.name }}"
                   data-employee-faculty="{{ user.faculty }}"
                   data-employee-image="/media/{{ user.image }}">
                <div class="image-box">
                  <img src="/media/{{ user.image }}" alt="{{ user.name }}">
                </div>
                <div class="card-header">
                  <h5 class="title">{{ user.name }}</h5>
                  <div class="employee-faculty">{{ user.faculty }}</div>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const assignedList = document.getElementById('assignedEmployeesList');
    const assignedInput = document.getElementById('assignedEmployeesInput');
    const assignedEmployees = new Set();

    // Function to update hidden input with assigned employee IDs
    function updateAssignedInput() {
        assignedInput.value = Array.from(assignedEmployees).join(',');
    }

    // Click handler for available employees
    document.querySelectorAll('.available-employee').forEach(card => {
        card.addEventListener('click', function() {
            const id = this.dataset.employeeId;
            if (assignedEmployees.has(id)) return;

            const clone = this.cloneNode(true);
            const removeBtn = document.createElement('div');
            removeBtn.className = 'remove-employee';
            removeBtn.innerHTML = '&times;';
            clone.insertBefore(removeBtn, clone.firstChild);
            clone.classList.remove('available-employee');

            const colWrapper = document.createElement('div');
            colWrapper.className = 'col-12';
            colWrapper.appendChild(clone);
            assignedList.appendChild(colWrapper);
            
            assignedEmployees.add(id);
            updateAssignedInput();
        });
    });

    // Click handler for remove button
    assignedList.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-employee')) {
            const employeeCard = e.target.closest('.ggg');
            const id = employeeCard.dataset.employeeId;
            assignedEmployees.delete(id);
            employeeCard.closest('.col-12').remove();
            updateAssignedInput();
        }
    });

    // Initialize the hidden input
    updateAssignedInput();
});
</script>
{% endblock %} 