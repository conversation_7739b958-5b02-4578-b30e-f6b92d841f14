.cd-about-section {
    
    padding: 30px 20px;
    background: linear-gradient(135deg, rgb(5, 5, 47), rgb(102, 40, 40), rgb(4, 41, 4));
   
}

.cd-about-container {
    padding-top: 70px;
    max-width: 1200px;
    margin: 0 auto;
}

.cd-about-header {
    text-align: center;
    margin-bottom: 40px;
}

.cd-about-title {
    font-size: 36px;
    font-weight: bold;
    color: #222;
}

.cd-about-subtitle {
    font-size: 18px;
    color: #555;
    max-width: 750px;
    margin: 10px auto 0;
}

.cd-about-body {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 40px;
}

.cd-about-text {
    flex: 1 1 500px;
}

.cd-about-text h3 {
    font-size: 24px;
    margin-top: 20px;
    color: #333;
}

.cd-about-text p {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.cd-about-values {
    list-style: none;
    padding-left: 0;
    margin-bottom: 30px;
}

.cd-about-values li {
    font-size: 16px;
    margin-bottom: 10px;
    color: #444;
}

.cd-about-image {
    flex: 1 1 400px;
    text-align: center;
}

.cd-about-image img {
    width: 100%;
    max-width: 500px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
    .cd-about-body {
        flex-direction: column-reverse;
    }

    .cd-about-title {
        font-size: 28px;
    }

    .cd-about-subtitle {
        font-size: 16px;
    }
}
