# SECURITY & DEBUGGING
DEBUG=True
SECRET_KEY=django-insecure-^ebudc35=3&_7738xxgolxrq7wg9nd%*sc^bgmi*cw&%(8o9%4

# HOSTS
ALLOWED_HOSTS=localhost,127.0.0.1

# DATABASE CONFIG (Development settings)
DB_ENGINE=django.db.backends.mysql
DB_NAME=click_db
DB_USER=root
DB_PASSWORD=VScode@123
DB_HOST=127.0.0.1
DB_PORT=3306

# STATIC & MEDIA (Optional)
STATIC_URL=/static/
MEDIA_URL=/media/

# EMAIL SETTINGS
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=wijx wqcv pffd udmk
DEFAULT_FROM_EMAIL=<EMAIL>

# TIMEZONE & LANGUAGE (Optional)
TIME_ZONE=Asia/Kathmandu
LANGUAGE_CODE=en-us
