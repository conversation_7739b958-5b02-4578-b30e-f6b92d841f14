# SECURITY & DEBUGGING
DEBUG=False
SECRET_KEY='django-insecure-^ebudc35=3&_7738xxgolxrq7wg9nd%*sc^bgmi*cw&%(8o9%4'

# HOSTS
ALLOWED_HOSTS=clickdigitals.com,clickdigitals.com.np

# DATABASE CONFIG (MySQL Example - change as needed)
DB_NAME=clickdigita_clickdigitals
DB_USER=Admin1
DB_PASSWORD=newhoster123@!
DB_HOST=localhost
DB_PORT=3306

# STATIC & MEDIA (Optional)
STATIC_URL=/static/
MEDIA_URL=/media/

# EMAIL SETTINGS (Optional if you're using SMTP)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.yourhost.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=emailpassword

# TIMEZONE & LANGUAGE (Optional)
TIME_ZONE=Asia/Kathmandu
LANGUAGE_CODE=en-us
