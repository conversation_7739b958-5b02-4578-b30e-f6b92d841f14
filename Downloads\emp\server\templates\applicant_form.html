{% extends 'header.html' %}
{% block content %}

    <div class="applicant-container">
        <form class="applicant-form-container" id="jobApplicationForm" method="POST" action="#" enctype="multipart/form-data">
            {% csrf_token %}
            <h2 class="applicant-form-title">Applicant Information - Join Our Team</h2>
            <p class="applicant-form-description">Please provide your information in the sections below.</p>

            {% if messages %}
                <div class="messages" style="color:azure">
                    {% for message in messages %}
                    <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}">
                        {{ message }}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            
            <!-- Personal Information Section -->
            <div class="applicant-section personal-details">
                <div class="section-title-wrapper">
                    <i class="fas fa-user-circle"></i>
                    <h3>Personal Information</h3>
                </div>
                <div class="form-grid">
                    <div class="form-field">
                        <label for="applicantName"><i class="fas fa-user"></i> Full Name</label>
                        <input type="text" id="applicantName" name="name" required placeholder="Enter your full name">
                    </div>
                    
                    <div class="form-field">
                        <label for="applicantAge"><i class="fas fa-birthday-cake"></i> Age</label>
                        <input type="number" id="applicantAge" name="age" required min="18" max="100" placeholder="Enter your age">
                    </div>
                    
                    <div class="form-field">
                        <label for="applicantGender"><i class="fas fa-venus-mars"></i> Gender</label>
                        <select id="applicantGender" name="gender" required>
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                            <option value="others">Other</option>
                        </select>
                    </div>

                    <div class="form-field">
                        <label for="applicantEmail"><i class="fas fa-envelope"></i> Email</label>
                        <input type="email" id="applicantEmail" name="email" required placeholder="Enter your email">
                    </div>
                </div>
            </div>

            <!-- Address Section -->
            <div class="applicant-section contact-details">
                <div class="section-title-wrapper">
                    <i class="fas fa-map-marked-alt"></i>
                    <h3>Address Information</h3>
                </div>
                <div class="form-grid">
                    <div class="form-field">
                        <label for="applicantPhone"><i class="fas fa-phone"></i> Contact</label>
                        <input type="tel" id="applicantPhone" name="contact" required pattern="[0-9]{10}" placeholder="Enter 10-digit number">
                    </div>
                    
                    <div class="form-field">
                        <label for="applicantCountry"><i class="fas fa-globe"></i> Country</label>
                        <input type="text" id="applicantCountry" name="country" required placeholder="Enter your country">
                    </div>

                    <div class="form-field full-width">
                        <label for="applicantAddress"><i class="fas fa-map-marker-alt"></i> Address</label>
                        <textarea id="applicantAddress" name="address" required placeholder="Enter your complete address"></textarea>
                    </div>
                </div>
            </div>

            <!-- Education & Experience Section -->
            <div class="applicant-section qualification-details">
                <div class="section-title-wrapper">
                    <i class="fas fa-graduation-cap"></i>
                    <h3>Education & Experience</h3>
                </div>
                <div class="form-grid">
                    <div class="form-field">
                        <label for="applicantExperience"><i class="fas fa-briefcase"></i> Experience (years)</label>
                        <input type="number" id="applicantExperience" name="experience" required min="0" max="50" placeholder="Years of experience">
                    </div>
                    
                    <div class="form-field">
                        <label for="applicantEducation"><i class="fas fa-graduation-cap"></i> Education</label>
                        <input type="text" id="applicantEducation" name="education" placeholder="Enter your education">
                    </div>

                    <div class="form-field">
                        <label for="applicantPhoto"><i class="fas fa-image"></i> Profile Image</label>
                        <input type="file" id="applicantPhoto" name="image" accept="image/*" required>
                    </div>
                    
                    <div class="form-field">
                        <label for="applicantResume"><i class="fas fa-file-alt"></i> Resume</label>
                        <input type="file" id="applicantResume" name="resume" accept=".pdf,.doc,.docx" required>
                    </div>

                    <div class="form-field full-width">
                        <label for="applicantSkills"><i class="fas fa-tools"></i> Skills</label>
                        <div class="skills-input-container">
                            <input type="text" id="applicantSkills" placeholder="Enter a skill and press Add">
                            <button type="button" id="addSkillBtn" class="add-skill-btn">
                                <i class="fas fa-plus"></i> Add
                            </button>
                        </div>
                        <div id="skillsContainer" class="skills-container"></div>
                        <input type="hidden" name="skills" id="skillsInput">
                    </div>
                   
                </div>
            </div>

            <div class="form-grid submit-container">
                <button type="submit" class="submit-button" id="submitApplication">
                    <i class="fas fa-paper-plane"></i> Submit Application
                </button>
            </div>
        </form>
    </div>

    <style>
:root {
    --primary-color: #1a73e8;
    --secondary-color: #0f428b;
    --accent-color: #d93025;
    --text-color: #e9ebf1;
    --background-color: #fefeff;
    --card-background: #f9f6f6;
    --border-color: #ababac;
    --shadow-color: rgba(0, 0, 0, 0.15);
    --transition-speed: 0.4s;
    --border-radius-small: 4px;
    --border-radius-medium: 6px;
    --border-radius-large: 10px;
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(90deg, #080f1c, #101d36);
    color: var(--text-color);
    line-height: 1.6;
}

.applicant-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-md);
}

.applicant-form-container {
    background: linear-gradient(90deg, #080f1c, #101d36);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-large);
    box-shadow: 0 8px 16px var(--shadow-color);
    animation: formFadeIn 0.8s ease-out;
    margin-top: var(--spacing-lg);
}

.applicant-form-title {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: var(--spacing-md);
    font-size: clamp(1.5rem, 4vw, 2rem);
    position: relative;
}

.applicant-form-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.applicant-form-description {
    text-align: center;
    color: var(--text-color);
    margin-bottom: var(--spacing-xl);
    font-size: clamp(0.9rem, 2vw, 1rem);
}

.applicant-section {
    border-radius: var(--border-radius-medium);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-speed) ease-in-out;
    opacity: 0;
    transform: translateY(20px);
}

.section-title-wrapper {
    display: flex;
    align-items: center;
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-md);
}

.section-title-wrapper i {
    font-size: 1.5rem;
    margin-right: var(--spacing-xs);
    color: var(--primary-color);
}

.section-title-wrapper h3 {
    color: var(--text-color);
    font-size: clamp(1rem, 3vw, 1.2rem);
    margin: 0;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.form-field {
    transition: all var(--transition-speed) ease-in-out;
}

.form-field.full-width {
    grid-column: 1 / -1;
}

label {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--text-color);
    font-weight: 500;
    font-size: 0.9rem;
}

label i {
    margin-right: 6px;
    color: var(--primary-color);
}

input, select, textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    font-size: 1rem;
    transition: all var(--transition-speed);
    background-color: var(--card-background);
    color: #333;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.2);
}

textarea {
    min-height: 120px;
    resize: vertical;
}

input[type="file"] {
    padding: 0.5rem;
    cursor: pointer;
}

input[type="file"]::file-selector-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: background-color var(--transition-speed);
}

input[type="file"]::file-selector-button:hover {
    background-color: var(--secondary-color);
}

.submit-container {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-lg);
}

.submit-button {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 1rem 2.5rem;
    border: none;
    border-radius: var(--border-radius-medium);
    font-size: clamp(1rem, 2vw, 1.1rem);
    cursor: pointer;
    transition: all var(--transition-speed);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    min-width: 200px;
}

.submit-button:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    box-shadow: 0 4px 10px rgba(26, 115, 232, 0.3);
    transform: translateY(-2px);
}

.submit-button:active {
    transform: translateY(0);
    box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .applicant-container {
        padding: var(--spacing-sm);
    }

    .applicant-form-container {
        padding: var(--spacing-lg);
    }

    .form-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .applicant-form-container {
        padding: var(--spacing-md);
    }

    .applicant-section {
        padding: var(--spacing-md);
    }

    .submit-button {
        width: 100%;
        padding: 0.8rem 1.5rem;
    }
}

/* Animations */
@keyframes formFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shake {
    10%, 90% { transform: translateX(-1px); }
    20%, 80% { transform: translateX(2px); }
    30%, 50%, 70% { transform: translateX(-4px); }
    40%, 60% { transform: translateX(4px); }
}

@keyframes loading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.submit-button.loading {
    pointer-events: none;
    opacity: 0.8;
}

.submit-button.loading i {
    animation: loading 1s linear infinite;
}

/* Custom select styling */
select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.8rem center;
    background-size: 1em;
    padding-right: 2.5rem;
}

/* Skills Section Styling */
.skills-input-container {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.skills-input-container input {
    flex: 1;
}

.add-skill-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: all var(--transition-speed);
    display: flex;
    align-items: center;
    gap: 5px;
}

.add-skill-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.skill-tag {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: skillFadeIn 0.3s ease-out;
    transition: all var(--transition-speed);
}

.skill-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.skill-tag .remove-skill {
    cursor: pointer;
    font-size: 0.8rem;
    opacity: 0.8;
    transition: opacity var(--transition-speed);
}

.skill-tag .remove-skill:hover {
    opacity: 1;
}

@keyframes skillFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes skillFadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

.skill-tag.removing {
    animation: skillFadeOut 0.3s ease-out forwards;
}

</style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('jobApplicationForm');
            const submitBtn = document.getElementById('submitApplication');
            const formSections = document.querySelectorAll('.applicant-section');

            // Add intersection observer for scroll animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    } else {
                        entry.target.style.opacity = '0';
                        entry.target.style.transform = 'translateY(20px)';
                    }
                });
            }, {
                threshold: 0.1
            });

            formSections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                observer.observe(section);
            });

            // Form validation and submission
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                if (!form.checkValidity()) {
                    const invalidFields = form.querySelectorAll(':invalid');
                    invalidFields.forEach(field => {
                        field.classList.add('shake');
                        setTimeout(() => field.classList.remove('shake'), 500);
                    });
                    return;
                }

                // Convert skills array to comma-separated string
                const skillsString = skills.join(',');
                document.getElementById('skillsInput').value = skillsString;

                submitBtn.classList.add('loading');
                submitBtn.innerHTML = '<i class="fas fa-spinner"></i> Submitting...';

                // Submit the form
                form.submit();
            });

            // Input validation feedback
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    if (this.validity.valid) {
                        this.style.borderColor = '';
                    } else {
                        this.style.borderColor = 'var(--accent-color)';
                    }
                });

                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-5px)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // File input preview
             const imageInput = document.getElementById('applicantPhoto');
            imageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // Image preview functionality can be added here
                    };
                    reader.readAsDataURL(file);
                }
            }); 

            // Skills functionality
            const skillsInput = document.getElementById('applicantSkills');
            const addSkillBtn = document.getElementById('addSkillBtn');
            const skillsContainer = document.getElementById('skillsContainer');
            const skillsHiddenInput = document.getElementById('skillsInput');
            let skills = [];

            function updateSkillsInput() {
                skillsHiddenInput.value = JSON.stringify(skills);
            }

            function addSkill(skill) {
                if (skill.trim() && !skills.includes(skill.trim())) {
                    skills.push(skill.trim());
                    const skillTag = document.createElement('div');
                    skillTag.className = 'skill-tag';
                    skillTag.innerHTML = `
                        ${skill.trim()}
                        <span class="remove-skill" data-skill="${skill.trim()}">
                            <i class="fas fa-times"></i>
                        </span>
                    `;
                    skillsContainer.appendChild(skillTag);
                    updateSkillsInput();

                    // Add remove event listener
                    skillTag.querySelector('.remove-skill').addEventListener('click', function() {
                        const skillToRemove = this.getAttribute('data-skill');
                        skillTag.classList.add('removing');
                        setTimeout(() => {
                            skills = skills.filter(s => s !== skillToRemove);
                            skillTag.remove();
                            updateSkillsInput();
                        }, 300);
                    });
                }
            }

            addSkillBtn.addEventListener('click', () => {
                addSkill(skillsInput.value);
                skillsInput.value = '';
            });

            skillsInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addSkill(skillsInput.value);
                    skillsInput.value = '';
                }
            });
        });
    </script> 
{% endblock content %} 
