{% extends 'base.html' %}
{% block title %}
Our Services
{% endblock title %}
{% block content %}
<style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #0a0f1e, #1c2a3a, #2c3e50);
    color: white;
    min-height: 100vh;
  }

  .container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    margin-top: 100px;
  }

  .services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 20px;
  }

  .service-card {
    background: rgba(31, 44, 59, 0.85);
    backdrop-filter: blur(12px);
    border-radius: 15px;
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
    padding: 20px;
    transition: all 0.35s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 18px 45px rgba(0, 0, 0, 0.6);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .service-header {
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
    margin-bottom: 15px;
  }

  .service-name {
    font-size: 1.4rem;
    font-weight: 600;
    color: #38bdf8;
    margin: 0;
    text-align: center;
  }

  .service-faculty {
    font-size: 0.9rem;
    color: #94a3b8;
    text-align: center;
    margin-top: 5px;
    text-transform: capitalize;
  }

  .service-price {
    font-size: 1.8rem;
    font-weight: 700;
    color: #38bdf8;
    text-align: center;
    margin: 15px 0;
    text-shadow: 0 2px 10px rgba(56, 189, 248, 0.3);
  }

  .characteristics-list {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    flex-grow: 1;
  }

  .characteristic-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.03);
    margin-bottom: 8px;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
  }

  .characteristic-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
  }

  .characteristic-item i {
    color: #38bdf8;
    margin-right: 10px;
  }

  .card-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
  }

  .action-btn {
    flex: 1;
    padding: 8px 16px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
  }

  .btn-edit {
    background-color: rgb(25, 134, 25);
    border: 1px solid rgb(35, 129, 35);
    color: white;
  }

  .btn-delete {
    background-color: rgb(163, 36, 36);
    border: 1px solid rgb(163, 36, 36);
    color: white;
  }

  .action-btn:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-3px);
  }


  @media (max-width: 1200px) {
    .container {
      max-width: 95%;
    }
  }

  @media (max-width: 992px) {
    .services-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .service-name {
      font-size: 1.2rem;
    }
    .service-price {
      font-size: 1.6rem;
    }
  }

  @media (max-width: 576px) {
    .services-grid {
      grid-template-columns: 1fr;
    }
    .section-header {
      flex-direction: column;
      gap: 15px;
      text-align: center;
    }
    .service-card {
      padding: 15px;
    }
  }
</style>

<div class="container">
  <div class="section-header">
    <h1 class="section-title">Our Services</h1>
  </div>

  <div class="services-grid">
    {% if services%}
    {% for service in services %}
    <div class="service-card">
      <div class="service-header">
        <a href="{%url 'create-project' service.id %}" class="service-name"><h3>{{ service.service_name }}</h3></a>
        <div class="service-faculty">{{ service.faculty }}</div>
      </div>

      <div class="service-price">${{ service.price }}</div>

      <ul class="characteristics-list">
        {% for char in service.characterstics.all %}
        <li class="characteristic-item">
          <i class="fas fa-check-circle"></i>
          {{ char.name }}
        </li>
        {% endfor %}
      </ul>

      <div class="card-actions">
        <a href="{% url 'update-service' service.id %}" class="action-btn btn-edit">
          Edit <i class="fas fa-edit"></i>
        </a>
        <a href="{% url 'delete-service' service.id %}" class="action-btn btn-delete">
          Delete <i class="fas fa-trash"></i>
        </a>
      </div>
    </div>
    {% endfor %}
    {% else %}
      <div class="no-projects-message">
        <i class="fas fa-inbox fa-3x"></i>
        <h2>No Services </h2>
        <p>We are not providing any services</p>
      </div>
    {% endif %}
  </div>
</div>
{% endblock %} 