{%load static%}
<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> 
    {% block title %}
        
    {% endblock title %}
        </title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">

<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Winky+Rough:ital,wght@0,300..900;1,300..900&display=swap" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">
</head>

<style>
    /* Navbar Styles */
    .navbar {
        background: rgba(30, 43, 58, 0.95);
        backdrop-filter: blur(10px);
        padding: 1rem 2rem;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        margin-bottom: 0px;
    }

    .logo-container {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .logo {
        height: 40px;
        width: auto;
    }

    .company-name {
        color: #0ea5e9;
        font-size: 1.5rem;
        font-weight: 700;
        text-decoration: none;
        font-family: "Poppins", sans-serif;
    }

    .nav-icons {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .nav-icon {
        color: #94a3b8;
        font-size: 1.2rem;
        text-decoration: none;
        transition: all 0.3s ease;
        position: relative;
        padding: 0.5rem;
        border-radius: 8px;
    }

    .nav-icon:hover {
        color: #0ea5e9;
        transform: translateY(-2px);
        background: rgba(14, 165, 233, 0.1);
    }

    .nav-icon.active {
        color: #0ea5e9;
        background: rgba(14, 165, 233, 0.1);
    }

    .icon-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #fd7e14;
        color: white;
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 10px;
        border: 2px solid rgba(30, 43, 58, 0.95);
    }

    .mobile-menu-btn {
        display: none;
        background: none;
        border: none;
        color: #94a3b8;
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .mobile-menu-btn:hover {
        color: #0ea5e9;
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
        .navbar {
            padding: 1rem;
        }

        .company-name {
            font-size: 1.2rem;
        }

        .mobile-menu-btn {
            display: block;
        }

        .nav-icons {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(30, 43, 58, 0.95);
            padding: 1rem;
            flex-direction: column;
            gap: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-icons.active {
            display: flex;
        }

        .nav-icon {
            width: 100%;
            padding: 0.8rem;
            text-align: center;
            border-radius: 8px;
        }

        .nav-icon:hover {
            background: rgba(14, 165, 233, 0.1);
        }
    }

    /* Adjust main content to account for fixed navbar */
    body {
        padding-top: 80px;
    }

    /* Existing styles */
    body{
       background: linear-gradient(135deg, #0a0f1e, #1c2a3a, #2c3e50);
    }
    *{
      font-family: "Poppins", sans-serif;
      font-weight: 450;
      font-style: normal;
      
    }
    .close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background: transparent;
        border: none;
        font-size: 2rem;
        font-weight: 700;
        color: #ccc;
        cursor: pointer;
        transition: color 0.3s ease;
        line-height: 1;
      }
      input.form-control, select.form-control {
        background-color: #2c3e50;  /* Dark slate */
        color: #ecf0f1;             /* Light text */
        padding: 10px;
        border: 1px solid #34495e;
        border-radius: 8px;
        margin-bottom: 10px;
        width: 100%;
        font-weight: 600;
        transition: box-shadow 0.3s ease, border-color 0.3s ease;
      }

      /* Style autocomplete suggestions */
      input:-webkit-autofill,
      input:-webkit-autofill:hover,
      input:-webkit-autofill:focus,
      input:-webkit-autofill:active {
          -webkit-text-fill-color: #ecf0f1 !important;
          -webkit-box-shadow: 0 0 0 30px #2c3e50 inset !important;
          transition: background-color 5000s ease-in-out 0s;
      }

      /* Style the datalist dropdown */
      input::-webkit-calendar-picker-indicator {
          filter: invert(1);
      }

      /* Override browser's default autocomplete styles */
      input:-webkit-autofill::first-line {
          font-family: "Poppins", sans-serif;
      }

      /* Style for autocomplete suggestions box */
      input.form-control::-webkit-list-button,
      input.form-control::-webkit-calendar-picker-indicator {
          opacity: 0;
      }

      @-webkit-keyframes autofill {
          to {
              color: #ecf0f1;
              background: #2c3e50;
          }
      }
      @keyframes autofill {
          to {
              color: #ecf0f1;
              background: #2c3e50;
          }
      }

      input.form-control:-webkit-autofill {
          -webkit-animation-name: autofill;
          -webkit-animation-fill-mode: both;
      }

      /* Style for the actual dropdown list */
      option {
          background-color: #2c3e50;
          color: #ecf0f1;
      }

      datalist {
          background-color: #2c3e50;
          color: #ecf0f1;
      }

      /* Additional form control focus styles */
      input.form-control:focus,
      select.form-control:focus {
          outline: none;
          border-color: #38bdf8;
          box-shadow: 0 0 6px 2px rgba(56, 189, 248, 0.4);
          background-color: #2c3e50;
          color: #ecf0f1;
      }

      .close-btn:hover {
        color: #f55;
      }
     a{
        text-decoration: none;
        text-align: center;
      }
    label{
        color: #fd7e14;
    }
      .faculty-badge {
    background: rgba(14, 165, 233, 0.15);
    color: #38bdf8;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 12px;
    border: 1px solid rgba(56, 189, 248, 0.3);
    width: fit-content;
  }
  .back-arrow {
    position: absolute;
    left: 15px;
    top:100px;
    transform: translateY(-50%);
    color: #38bdf8;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
   
  }

  .back-arrow:hover {
    color: white;
    transform: translateY(-50%) translateX(-5px);
  }

  .back-arrow i {
    font-size: 1.2rem;
  }
    .gg{
        
        border-radius: 15px;
        white-space: nowrap;      /* Prevent text from wrapping to next line */
        overflow: hidden;         /* Hide the overflowed part */
        text-overflow: clip;      /* Just clip (no ellipsis) */
        transition: transform 0.3s ease-in-out,box-shadow 0.3s ease-in-out;
        background: #1e2b3a;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6);
        color: white;
        
    }
    .gg:hover {
          
          box-shadow: 0 10px 25px rgba(255, 255, 255, 0.6);
            }
</style>
<style>
  .btn.aa {
    border: 1px solid white;
    color: white;
    background-color: #2a2f3b;
    transition: all 0.2s ease-in-out;
  }

  .btn.aa:hover {
    background-color: #0ea5e9;
    color: white;
    
    cursor: pointer;
  }
 
  .btn-check:checked + .btn.aa {
    background-color: #0ea5e9;
    color: white;
    border: 1px solid#0ea5e9;
  }

  input{
      color: rgb(168, 168, 168);
      font-weight: 500;
  }
  input[type="file"].form-control {
  background-color: #2c3e50;  /* Same dark background */
  color: #ecf0f1;             /* Same light text */
  padding: 10px;
  border: 1px solid #34495e;
  border-radius: 8px;
  margin-bottom: 10px;
  width: 100%;
  font-weight: 600;
  box-sizing: border-box;
  cursor: pointer;
}

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding-bottom: 10px;
    border-bottom: 2px solid white;
  }

  .section-title {
    font-size: 2rem;
    font-weight: 700;
    color:#ffffff;
  }

  .add-service-btn {
    background-color: #38bdf8;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid #38bdf8;
  }

  .add-service-btn:hover {
    background-color: #2a2f3b;
    border-color: white;
    transform: translateY(-3px);
  }


input[type="file"].form-control::file-selector-button {
  display: none; /* Hide the default button so whole input looks uniform */
}

  .form-header{
      margin-bottom: 10px;
      font-size: 30px;
      color: #198754;
      text-align: center;
      border-bottom: 2px solid gray;
      padding-bottom: 5px;
      margin-bottom: 15px;
  }
</style>
<style>
  .skill-tag {
    background-color: #e0e7ff;
    color: #1e3a8a;
    padding: 5px 10px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
  }
  .skill-tag span {
    cursor: pointer;
    font-weight: bold;
  }

  .modal-content {
    background: rgba(31, 44, 59, 0.95);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    color: #e2e8f0;
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
}

.modal-title {
    color: #e2e8f0;
    font-weight: 600;
}
  .no-projects-message {
    text-align: center;
    padding: 40px;
    background: rgba(31, 44, 59, 0.85);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .no-projects-message i {
    color: #38bdf8;
    margin-bottom: 20px;
  }

  .no-projects-message h2 {
    color: #ffffff;
    font-size: 1.8rem;
    margin-bottom: 10px;
  }

  .no-projects-message p {
    color: #94a3b8;
    font-size: 1.1rem;
  }

  </style>
<style>
   


  .dropbtn {
    background: rgba(30, 43, 58, 0.95);
    color: #94a3b8;
    font-size: 1.2rem;
    padding: 0px 0px;
    border: none;
    cursor: pointer;
    border-radius: 6px;
    min-width: 115px;
  }
  .dropbtn:hover{
    color: #0ea5e9;
    z-index: 999;
        background: rgba(14, 165, 233, 0.1);
        z-index: 999;
  }
  .dropdown {
    background: rgba(30, 43, 58, 0.95);
    color: #94a3b8;
    font-size: 1.2rem;
    position: relative;
    display: inline-block;
    width: auto;
  
  }

  .dropdown-content {
    display: none;
    position: absolute;
    background: rgba(30, 43, 58, 0.95);
    min-width: 120px;
    margin-top:5px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
    border-radius: 6px;
    z-index: 999;

  }

  .dropdown-content a {
    color: #f0f0f0;
    padding: 8px 14px;
    font-size: 1rem;
    background-color: rgba(30, 43, 58, 0.95);
    text-decoration: none;
    display: block;
    overflow: hidden;
  }

  .dropdown:hover .dropdown-content {
    display: block;
  }
  .dropbtn:hover .dropdown-content {
    display: block;
  }

  
   .nav-icon {
        color: #94a3b8;
        font-size: 1.2rem;
        text-decoration: none;
        transition: all 0.3s ease;
        position: relative;
        padding: 0.5rem;
        border-radius: 8px;
    }

    .nav-icon:hover {
        color: #0ea5e9;
        transform: translateY(-2px);
        background: rgba(14, 165, 233, 0.1);
    }
    .dropdown-content a:hover{
        color: #0ea5e9;
        background: rgba(14, 165, 233, 0.1);
        z-index: 999;
    }

</style>


<body>
    <!-- Navbar -->
    <nav class="navbar">
        <a href="{% url 'employee-list' %}" class="logo-container">
            <img src="{% static 'images/clickdigital.png' %}" loading="lazy" alt="Company Logo" class="logo">
        </a>
              <div class="nav-icons">
            <div class="dropdown nav-icon">
      <button class="dropbtn ">Fields▾</button>
      <div class="dropdown-content">
        <a href="{% url 'service-list' %}">Services</a>
        <a href="{% url 'vacancy' %}">Vacancy</a>
        <a href="{% url 'employee-list'%}">Employee</a>
        <a href="{% url 'vacancy_applicants_list'%}">Applicants</a>
        <a href="{%url 'blog_lists'%}">Blogs</a>
      </div>
      </div>

        <div class="nav-icons">
            <div class="dropdown nav-icon">
      <button class="dropbtn ">Projects ▾</button>
      <div class="dropdown-content">
        <a href="{% url 'project-list' %}">New</a>
        <a href="{% url 'startproject-list' %}">Ongoing</a>
        <a href="{% url 'deploy-list' %}">Past</a>
      </div>
      </div>

      <div class="nav-icons">
            <div class="dropdown nav-icon">
      <button class="dropbtn dropdown ">Connect ▾</button>
      <div class="dropdown-content">
        <a href="{% url 'contact_list' %}">Message</a>
        <a href="{% url 'collab_list' %}">Collab</a>
      </div>
      </div>



      <div class="nav-icons">
            <div class="dropdown nav-icon">
      <button class="dropbtn ">Add ▾</button>
      <div class="dropdown-content">
        <a href="{% url 'create-service' %}">Services</a>
        <a href="{% url 'create_vacancy' %}">Vacancy</a>
        <a href="{% url 'create'%}">Employee</a>
        <a href="{%url 'create_blog'%}">Blogs</a>
      </div>
      </div>

      <div class="nav-icons">
        <div class="dropdown nav-icon">
          <button class="dropbtn ">Actions ▾</button>
          <div class="dropdown-content">
            <a href="{% url 'logout' %}">Logout</a>
            <a href="{% url 'register'%}">Register</a>
          </div>
        </div>
      </div>
    </nav>
   {% if messages %}
  <div class="alert-container">
    {% for message in messages %}
      <div class="alert ">
        <span class="alert-text">{{ message }}</span>
        <button class="close-btn" onclick="this.parentElement.remove();">&times;</button>
      </div>
    {% endfor %}
  </div>
{% endif %}

<style>
  .alert-container {
    position: fixed;
    top: 100px;
    right: 20px;
    width: auto;
    z-index: 9999;
  }

  .alert {
    padding: 14px 16px;
    margin-bottom: 12px;
    border-radius: 8px;
    font-family: sans-serif;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    outline: none;
    border-color: #38bdf8;
    box-shadow: 0 0 6px 2px rgba(56, 189, 248, 0.4);
    background-color: #34495e;
    opacity: 1;
    transition: opacity 0.5s ease;
  }

  .alert.success { background-color: #38a169; }  /* green */


  .alert-text {
    flex: 1;
    padding-right: 10px;
    margin-right: 20px;
  }

  .close-btn {
    background: none;
    border: none;
    color: inherit;
    font-size: 20px;
    line-height: 1;
    cursor: pointer;
  }
</style>

<script>
  window.addEventListener('DOMContentLoaded', () => {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        setTimeout(() => alert.remove(), 500);
      }, 3000); // 4 seconds
    });
  });
</script>

    {% block content %}
        
    {% endblock content %}

    <script>
        // Mobile menu toggle
        document.querySelector('.mobile-menu-btn').addEventListener('click', function() {
            document.querySelector('.nav-icons').classList.toggle('active');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const navIcons = document.querySelector('.nav-icons');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            
            if (!navIcons.contains(event.target) && !mobileMenuBtn.contains(event.target)) {
                navIcons.classList.remove('active');
            }
        });

        // Set active nav icon based on current page
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navIcons = document.querySelectorAll('.nav-icon');
            
            navIcons.forEach(icon => {
                if (icon.getAttribute('href') === currentPath) {
                    icon.classList.add('active');
                }
            });
        });
    </script>
    <script>

</script>
    <!-- Your existing scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js" integrity="sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq" crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</body>
</html>