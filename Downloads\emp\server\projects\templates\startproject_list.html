{% extends 'base.html' %}
{% load static %}
{% block title %}
Ongoing Projects
{% endblock title %}
{% block content %}
<style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #0a0f1e, #1c2a3a, #2c3e50);
    color: white;
    min-height: 100vh;
  }

  .container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    margin-top: 100px;
  }

  .back-arrow {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #38bdf8;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border-radius: 50%;
    background: rgba(56, 189, 248, 0.1);
    border: 1px solid rgba(56, 189, 248, 0.2);
    width: 40px;
    height: 40px;
  }

  .back-arrow:hover {
    color: white;
    background: rgba(56, 189, 248, 0.2);
    transform: translateY(-50%) translateX(-5px);
  }

  .back-arrow i {
    font-size: 1.2rem;
  }

  .back-arrow span {
    font-weight: 500;
  }


  .project-card {
    background: rgba(31, 44, 59, 0.85);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;
  }

  .project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(56, 189, 248, 0.15);
    border-color: rgba(56, 189, 248, 0.3);
  }

  .project-header {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .project-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .project-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .project-company {
    font-size: 1rem;
    color: #94a3b8;
  }

  .project-status {
    display: inline-block;
    padding: 6px 16px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
  }

  .status-pending {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
  }

  .status-started {
    background: rgba(234, 179, 8, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(234, 179, 8, 0.3);
  }

  .status-completed {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
  }

  .project-meta {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-end;
  }

  .meta-items {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
  }

  .days-remaining {
    font-size: 0.9rem;
    color: #94a3b8;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    white-space: nowrap;
  }

  .days-remaining i {
    color: #38bdf8;
  }

  .days-remaining.urgent {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
  }

  .days-remaining.urgent i {
    color: #ef4444;
  }

  .project-details {
    display: block;
    margin-bottom: 20px;
  }

  .company-section {
    width: 300px;
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 12px;
  }

  .detail-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .detail-label {
    font-size: 0.85rem;
    color: #94a3b8;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .detail-value {
    font-size: 0.95rem;
    color: #ffffff;
    font-weight: 500;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .project-description {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
  }

  .description-title {
    font-size: 1rem;
    color: #38bdf8;
    margin-bottom: 10px;
    font-weight: 600;
    border-bottom: 2px solid rgba(56, 189, 248, 0.3);
    padding-bottom: 8px;
  }

  .description-content {
    color: #e2e8f0;
    font-size: 0.95rem;
    line-height: 1.6;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }

  .assigned-employees {
    margin-top: 20px;
  }

  .assigned-title {
    font-size: 1rem;
    color: #38bdf8;
    margin-bottom: 15px;
    font-weight: 600;
    border-bottom: 2px solid rgba(56, 189, 248, 0.3);
    padding-bottom: 8px;
  }

  .employee-list {
    display: flex;
    flex-direction: row;
    gap: 15px;
    overflow-x: auto;
    padding: 10px 0;
  }

  .employee-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 12px;
    text-align: center;
    transition: all 0.3s ease;
    min-width: 120px;
    flex-shrink: 0;
  }

  .employee-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }

  .employee-image {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    margin: 0 auto 10px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    object-fit: cover;
  }

  .employee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .employee-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .employee-info h6 {
    font-size: 0.9rem;
    color: #ffffff;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
  }

  .employee-info small {
    font-size: 0.8rem;
    color: #94a3b8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  @media (max-width: 992px) {
    .project-header {
      grid-template-columns: 1fr;
    }

    .project-meta {
      align-items: flex-start;
    }

    .project-details {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .project-details {
      grid-template-columns: 1fr;
    }

    .employee-list {
      gap: 10px;
    }
    
    .employee-card {
      min-width: 100px;
    }
    
    .employee-image {
      width: 60px;
      height: 60px;
    }
  }

  .details-description-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    margin-top: 20px;
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
    border-right: 2px solid rgba(255, 255, 255, 0.1);
    padding-right: 20px;
  }

  .company-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 12px;
    height: fit-content;
    transition: all 0.3s ease;
    width: 100%;
  }

  .company-section:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .detail-label {
    font-size: 0.85rem;
    color: #94a3b8;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .detail-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ffffff;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .description-column {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 12px;
    height: 100%;
    transition: all 0.3s ease;
  }

  .description-column:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .button-row {
    display: flex;
    flex-direction: row;
    gap: 12px;
    width: 100%;
  }

  .action-btn {
    flex: 1;
    border-radius: 8px;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: white;
    text-decoration: none;
    position: relative;
    z-index: 1;
  }

  .btn-update {
    background-color: #38bdf8;
    border: 1px solid #38bdf8;
  }

  .btn-deploy {
    background-color: #10b981;
    border: 1px solid #10b981;
  }

  .action-btn:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  @media (max-width: 992px) {
    .details-description-container {
      grid-template-columns: 1fr;
    }

    .left-column {
      gap: 15px;
    }
  }

  .project-faculty {
    font-size: 0.9rem;
    color: #ffffff;
    margin-bottom: 4px;
    font-weight: 500;
    background: rgba(56, 189, 248, 0.15);
    padding: 4px 12px;
    border-radius: 6px;
    display: inline-block;
    border: 1px solid rgba(56, 189, 248, 0.2);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: fit-content;
  }

  .project-title {
    margin-top: 4px;
  }

  .top-row {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 8px;
  }

  .price-tag {
    font-size: 0.9rem;
    font-weight: 600;
    color: #10b981;
    background: rgba(16, 185, 129, 0.15);
    padding: 4px 12px;
    border-radius: 6px;
    display: inline-block;
    border: 1px solid rgba(16, 185, 129, 0.3);
    white-space: nowrap;
  }
</style>

<div class="container">
  <div class="section-header">
    <h1 class="section-title">Ongoing Projects</h1>
    <a href="{% url 'project-list' %}" class="add-service-btn">
       New Projects
    </a>
  </div>
  {%if projects_data%}
  {% for project_data in projects_data %}
    {% with project=project_data.project days_remaining=project_data.days_remaining is_urgent=project_data.is_urgent %}
    <div class="project-card">
        <div class="project-header">
            <div class="project-info">
                <div class="top-row">
                    <span class="project-faculty">{{ project.project.servicedetails.faculty|title }}</span>
                    <span class="price-tag">${{ project.price }}</span>
                </div>
                <h3 class="project-title">{{ project.project.name }}</h3>
            </div>
            <div class="project-meta">
                <div class="meta-items">
                    <span class="project-status status-{{ project.status }}">{{ project.status|title }}</span>
                    <div class="days-remaining {% if is_urgent %}urgent{% endif %}">
                        <i class="fas fa-clock"></i>
                        <span>{{ days_remaining }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="details-description-container">
            <div class="left-column">
                <div class="company-section">
                    <span class="detail-label">Company</span>
                    <span class="detail-value">{{ project.project.company_name }}</span>
                </div>
                <div class="button-row">
                    <a href="{% url 'update-startproject' project.id %}" class="action-btn btn-update">
                        <i class="fas fa-edit"></i>
                        Update
                    </a>
                    <a href="{% url 'deploy-project' project.id %}" class="action-btn btn-deploy">
                        <i class="fas fa-rocket"></i>
                        Deploy
                    </a>
                </div>
            </div>
            <div class="description-column">
                <h4 class="description-title">Description</h4>
                <p class="description-content">{{ project.description }}</p>
            </div>
        </div>
        
        <div class="assigned-employees">
            <h4 class="assigned-title">Assigned Employees</h4>
            <div class="employee-list">
              
                {% for user in project.assigned_users.all %}
                <div class="employee-card">
                    <div class="employee-image">
                        {% if user.image %}
                            <img src="{{ user.image.url }}" alt="{{ user.name }}">
                        {% else %}
                            <img src="{% static 'images/default-avatar.png' %}" alt="{{ user.name }}">
                        {% endif %}
                    </div>
                    <div class="employee-info">
                        <h6>{{ user.name }}</h6>
                        <small>{{ user.faculty }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endwith %}
  {% endfor %}
  {% else %}
      <div class="no-projects-message">
        <i class="fas fa-inbox fa-3x"></i>
        <h2>No Pending Projects</h2>
        <p>There are no Ongoing Projects</p>
      </div>
    {% endif %}
</div>
{% endblock %}
