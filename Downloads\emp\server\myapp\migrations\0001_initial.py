# Generated by Django 5.1.2 on 2025-06-13 02:49

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Skill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('age', models.IntegerField(default=20)),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female'), ('others', 'Others')], max_length=10)),
                ('position', models.Char<PERSON>ield(max_length=50)),
                ('image', models.ImageField(upload_to='employee-image')),
                ('email', models.EmailField(max_length=100)),
                ('country', models.TextField(max_length=50)),
                ('address', models.TextField(max_length=50)),
                ('contact', models.CharField(max_length=15)),
                ('faculty', models.CharField(choices=[('development', 'Development'), ('digitalmarketing', 'Digital Marketing')], default='development', max_length=20)),
                ('level', models.CharField(choices=[('intern', 'Intern'), ('junior', 'Junior'), ('senior', 'Senior')], default='intern', max_length=20)),
                ('salary', models.BigIntegerField(default=10000)),
                ('experience', models.IntegerField(default=0)),
                ('job_type', models.CharField(choices=[('parttime', 'Part Time'), ('fulltime', 'Full Time')], default='fulltime', max_length=20)),
                ('education', models.CharField(default='not specified', max_length=100)),
                ('project_ratings', models.JSONField(blank=True, default=list)),
                ('rating', models.IntegerField()),
                ('projects', models.IntegerField(default=0, null=True)),
                ('skills', models.ManyToManyField(blank=True, to='myapp.skill')),
            ],
        ),
    ]
