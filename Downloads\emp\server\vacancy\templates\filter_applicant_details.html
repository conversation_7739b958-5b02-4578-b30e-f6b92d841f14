{% extends './templates/base.html' %}
{% block title %}
Applicant Detail
{% endblock title %}

{% block content %}
<style> 
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #0a0f1e, #1c2a3a, #2c3e50);
    color: white;
    min-height: 100vh;
}

.container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    margin-top: 100px;
}

.details {
    display: grid;
    grid-template-rows: auto 15px auto 15px auto;
    grid-template-columns: 23% 15px 23% 15px 23% 15px 23%;
    gap: 10px;
    margin: 15px 0;
}

.gg {
    background: rgba(31, 44, 59, 0.85);
    backdrop-filter: blur(12px);
    border-radius: 15px;
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
    padding: 15px;
    transition: all 0.35s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.gg:hover {
    transform: translateY(-5px);
    box-shadow: 0 18px 45px rgba(0, 0, 0, 0.6);
    border-color: rgba(255, 255, 255, 0.2);
}

.title {
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: #e2e8f0;
    transition: all 0.3s ease;
}

.gg:hover .title {
    border-bottom-color: rgba(255, 255, 255, 0.2);
}

.grid-content1 {
    font-size: 3.5rem;
    font-weight: 700;
    text-align: center;
    color: #38bdf8;
    text-shadow: 0 2px 10px rgba(56, 189, 248, 0.3);
    transition: all 0.3s ease;
}

.gg:hover .grid-content1 {
    transform: scale(1.05);
    text-shadow: 0 2px 15px rgba(56, 189, 248, 0.4);
}

.grid-content4 {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    color: #38bdf8;
    line-height: 1.2;
    text-transform: capitalize;
    text-shadow: 0 2px 10px rgba(56, 189, 248, 0.3);
}

.grid-content2 {
    font-size: 0.95rem;
    padding: 5px 10px;
    color: #e2e8f0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.grid-content2 div {
    font-weight: 500;
    color: #e2e8f0;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.grid-content2 div:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.grid-content2 i {
    font-size: 1.1rem;
    color: #38bdf8;
}

.grid-contentbtn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 15px;
    padding: 15px 5px 5px 5px;
    margin-top: auto;
}

.grid-contentbtn a {
    text-decoration: none;
    color: white;
    font-size: 0.95rem;
    padding: 12px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
}

.edit {
    background-color: rgb(25, 134, 25);
    border: 1px solid rgb(35, 129, 35);
}

.delete {
    background-color: rgb(163, 36, 36);
    border: 1px solid rgb(163, 36, 36);
}

.grid-contentbtn a:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-5px);
}

.grid-contentbtn a i {
    color: white;
}

.grid-4 {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
    background: rgba(31, 44, 59, 0.85);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.35s ease;
}

.grid-4:hover {
    transform: translateY(-5px);
    box-shadow: 0 18px 45px rgba(0, 0, 0, 0.6);
    border-color: rgba(255, 255, 255, 0.2);
}

.grid-4 img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.grid-4:hover img {
    transform: scale(1.05);
}

/* Grid Layout */
.grid-1 { grid-row: 1/2; grid-column: 1/2; }
.grid-2 { grid-row: 1/2; grid-column: 3/4; }
.grid-3 { grid-row: 1/2; grid-column: 5/6; }
.grid-4 { grid-row: 1/4; grid-column: 7/8; }
.grid-5 { grid-row: 3/6; grid-column: 1/2; }
.grid-6 { grid-row: 3/6; grid-column: 3/4; }
.grid-7 { grid-row: 3/4; grid-column: 5/6; }
.grid-8 { grid-row: 5/6; grid-column: 5/6; }
.grid-9 { grid-row: 5/6; grid-column: 7/8; }

/* Modal Styling */


.btn-close {
    color: white;
    opacity: 0.8;
}

.btn-danger {
    background-color: rgb(163, 36, 36);
    border: 1px solid rgb(163, 36, 36);
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background-color: #2a2f3b;
    border: 1px solid white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
    }
    .details {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: auto;
        gap: 15px;
    }
    
    [class^="grid-"] {
        grid-column: auto;
        grid-row: auto;
    }
    
    .grid-4 {
        grid-column: span 3;
        aspect-ratio: 16/9;
        max-height: 300px;
    }
    .grid-1, .grid-2, .grid-3 {
        min-height: 160px;
    }
    .grid-7 {
        min-height: 140px;
    }
    .grid-8 {
        min-height: 110px;
    }
}

@media (max-width: 992px) {
    .container {
        max-width: 98%;
        padding: 15px;
    }
    .details {
        gap: 12px;
    }
    .gg {
        padding: 12px;
    }
    .grid-content1 {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    .details {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .grid-4 {
        grid-column: span 2;
        max-height: 250px;
    }
    
    .grid-content1, .grid-content4 {
        font-size: 2.5rem;
    }
    .gg {
        padding: 10px;
    }
    .grid-1, .grid-2, .grid-3 {
        min-height: 140px;
    }
    .grid-7 {
        min-height: 120px;
    }
    .grid-8 {
        min-height: 100px;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 10px;
    }
    .details {
        grid-template-columns: 1fr;
        gap: 10px;
        margin: 10px 0;
    }
    
    .grid-4 {
        grid-column: span 1;
        max-height: 200px;
    }
    
    .grid-content1, .grid-content4 {
        font-size: 2rem;
    }
    
    .grid-contentbtn {
        padding: 12px 0 0 0;
        gap: 12px;
    }
    
    .grid-contentbtn a {
        padding: 10px 12px;
    }
    
    .grid-content2 {
        padding: 0;
    }
    
    .title {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }
    .grid-1, .grid-2, .grid-3, .grid-7, .grid-8 {
        min-height: auto;
        padding: 15px;
    }
    .grid-contentbtn {
        padding: 0;
    }
}
.position{
    font-size: 1.5rem;
    font-weight: 550;
}
</style>

<div class="container">
    <a href="javascript:history.back()" class="back-arrow">
      <i class="fas fa-arrow-left"></i>
    </a>
   <div class="details">
    <div class="grid-1 gg">
        <div class="title">Experience (yrs)</div>
        <div class="grid-content1">{{applicant.experience}}</div>
    </div>
    <div class="grid-2 gg">
        <div class="title">Position</div>
        <div class="grid-content1 position">{{applicant.vacancydetails.position}}</div>
    </div>
    <div class="grid-3 gg">
        <div class="title">Rating</div>
        <div class="grid-content1">{{interview.rating}}</div>
    </div>
    <div class="grid-4">
        <img src="/media/{{applicant.image}}" alt="{{applicant.name}}" loading="lazy">
    </div>
    <div class="grid-5 gg">
        <div class="title">Professional Info</div>
        <div class="grid-content2">
            <div><i class="fa-solid fa-user"></i>{{applicant.name}}</div>
            <div><i class="fa-solid fa-calendar"></i>{{applicant.age}} years</div>
            <div><i class="fa-solid fa-globe"></i>{{applicant.country}}</div>
            <div><i class="fa-solid fa-location-dot"></i>{{applicant.address}}</div>
            <div><i class="fa-solid fa-building"></i>{{applicant.vacancydetails.faculty}}</div>
            <div><i class="fa-solid fa-briefcase"></i>{{applicant.vacancydetails.position}}</div>
        </div>
    </div>
    <div class="grid-6 gg">
        <div class="title">Skills</div>
        <div class="grid-content2">
            {% for skill in applicant.skills.all %}
            <div><i class="fa-solid fa-check"></i>{{skill}}</div>
            {% endfor %}
        </div>
    </div>
    <div class="grid-7 gg">
        <div class="title">Level</div>
        <div class="grid-content4">{{applicant.vacancydetails.level}}</div>
    </div>
    <div class="grid-8 gg">
        <div class="title">Actions</div>
        <div class="grid-contentbtn">
            <a class="edit" href="{% url 'hire' interview.id applicant.id %}">
                Hire<i class="fa-solid fa-user-pen"></i>
            </a>
            <a href="#" class="delete" data-bs-toggle="modal" data-bs-target="#deleteModal" data-user-id="{{ applicant.id }}">
                Reject<i class="fa-solid fa-trash"></i>
            </a>
        </div>
    </div>
    <div class="grid-9 gg">
        <div class="title">Contact Info</div>
        <div class="grid-content2">
            <div><i class="fa-solid fa-phone-volume"></i>{{applicant.contact}}</div>
            <div><i class="fa-solid fa-envelope"></i>{{applicant.email}}</div>
        </div>
    </div>
    

</div>

<div class="grid-contentbtn">
<a href="{{base_url}}{{ applicant.resume.url}}"class='edit' a> View Resume </a>
</div>




<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
       <form method="post" id="deleteForm" action="{% url 'delete_filter' applicant.id %}">
            {% csrf_token %}
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Rejection</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to reject {{applicant.name}}?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Yes,Reject</button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}





